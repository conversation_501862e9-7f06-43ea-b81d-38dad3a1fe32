/**
 * Salonier AI Typography System
 * Inspired by Claude.ai interface design
 *
 * Research Sources:
 * - Claude.ai interface (claude.ai)
 * - Typography analysis from "My Styrene Soul: A Short Affair with <PERSON>.ai"
 *
 * Typography Philosophy:
 * - Clear hierarchy with generous sizing
 * - Comfortable line heights for readability
 * - Rounded, approachable sans-serif for UI
 * - Ser<PERSON> for emphasis and elegance in hero text
 * - Consistent letter spacing for polish
 */

/**
 * FONT FAMILIES
 *
 * <PERSON> uses:
 * - Headers: Galaxie Copernicus Book (serif)
 * - Body/UI: Styrene B Family (rounded sans-serif)
 * - Secondary: Tiempos Text (serif)
 *
 * Mobile fallbacks (React Native defaults):
 * - iOS: San Francisco (system font - rounded, modern)
 * - Android: Roboto (system font - clean, readable)
 *
 * For consistency, we use system fonts with specific weight/spacing to match <PERSON>'s feel
 */
export const fontFamilies = {
  // Primary UI text (buttons, inputs, body) - rounded, friendly
  ui: {
    ios: 'System',
    android: 'Roboto',
    weight: {
      regular: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
    },
  },
  // Hero/display text (welcome messages, large headings) - elegant
  display: {
    ios: 'System',
    android: 'Roboto',
    weight: {
      regular: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
    },
  },
} as const;

/**
 * FONT SIZES
 *
 * Claude's hierarchy (estimated from interface):
 * - Hero/greeting: ~48-56px (large, welcoming)
 * - Screen title: ~28-32px
 * - Section heading: ~20-24px
 * - Body text: ~16-17px (generous, readable)
 * - Secondary/meta: ~13-14px
 * - Small/caption: ~11-12px
 *
 * Mobile scaling: slightly smaller than web for thumb-friendly interaction
 */
export const fontSize = {
  // Display sizes (hero, welcome messages)
  display: {
    large: 48,    // Hero greeting (special screens only)
    medium: 32,   // Welcome prompts (Claude mobile real: ~32-36px)
    small: 28,    // Section heroes
  },

  // Heading sizes
  heading: {
    h1: 28,       // Screen titles "Salonier AI"
    h2: 24,       // Major sections
    h3: 20,       // Subsections
    h4: 18,       // Minor headings
  },

  // Body text (most common)
  body: {
    large: 17,    // Primary input text (Claude uses ~17px)
    medium: 16,   // Standard body text
    small: 15,    // Compact body text
  },

  // UI elements
  ui: {
    button: 15,   // Button labels
    input: 17,    // Input placeholder/text (matches body.large)
    label: 14,    // Form labels
  },

  // Supporting text
  secondary: {
    large: 14,    // Secondary info
    medium: 13,   // Metadata, timestamps
    small: 12,    // Captions
  },

  // Tiny text (sparingly)
  caption: {
    large: 11,    // Legal, footnotes
    small: 10,    // Badge text
  },
} as const;

/**
 * LINE HEIGHTS
 *
 * Claude uses generous line heights for breathing room
 * Formula: fontSize * multiplier
 * - Display: 1.1-1.2 (tight, impactful)
 * - Headings: 1.2-1.3 (balanced)
 * - Body: 1.65 (comfortable reading - measured from Claude web: 16px → 26.4px)
 */
export const lineHeight = {
  display: {
    large: 52,    // 48 * 1.083
    medium: 40,   // 32 * 1.25
    small: 34,    // 28 * 1.214
  },
  heading: {
    h1: 34,       // 28 * 1.214
    h2: 30,       // 24 * 1.25
    h3: 26,       // 20 * 1.3
    h4: 24,       // 18 * 1.333
  },
  body: {
    large: 28,    // 17 * 1.647 (current Salonier value)
    medium: 26,   // 16 * 1.65 (Claude web measured: 16px → 26.4px)
    small: 22,    // 15 * 1.467
  },
  ui: {
    button: 20,   // 15 * 1.333
    input: 28,    // 17 * 1.647 (matches body.large)
    label: 18,    // 14 * 1.286
  },
  secondary: {
    large: 20,    // 14 * 1.429
    medium: 18,   // 13 * 1.385
    small: 16,    // 12 * 1.333
  },
  caption: {
    large: 14,    // 11 * 1.273
    small: 13,    // 10 * 1.3
  },
} as const;

/**
 * LETTER SPACING
 *
 * Claude uses negative letter spacing for polished, tight feel
 * - Display/headings: -0.5 to -0.8 (tight, impactful)
 * - Body: -0.1 to -0.3 (subtle tightening)
 * - Small text: 0 to -0.1 (preserve readability)
 */
export const letterSpacing = {
  display: {
    large: -0.8,
    medium: -0.6,
    small: -0.5,
  },
  heading: {
    h1: -0.5,
    h2: -0.4,
    h3: -0.3,
    h4: -0.2,
  },
  body: {
    large: -0.2,
    medium: -0.1,
    small: -0.1,
  },
  ui: {
    button: -0.1,
    input: -0.2,
    label: 0,
  },
  secondary: {
    large: 0,
    medium: 0,
    small: 0,
  },
  caption: {
    large: 0,
    small: 0,
  },
} as const;

/**
 * FONT WEIGHTS
 *
 * Claude uses medium-to-bold weights for clarity
 * - Display: 700-800 (bold, commanding)
 * - Headings: 600-700 (strong hierarchy)
 * - Body: 400-500 (readable, balanced)
 * - Secondary: 400-500 (subtle)
 */
export const fontWeight = {
  display: {
    large: '700' as const,
    medium: '700' as const,
    small: '700' as const,
  },
  heading: {
    h1: '800' as const,  // Screen titles (extra bold)
    h2: '700' as const,
    h3: '700' as const,
    h4: '600' as const,
  },
  body: {
    large: '400' as const,  // Regular for body
    medium: '400' as const,
    small: '400' as const,
  },
  ui: {
    button: '600' as const,  // Semibold for buttons
    input: '400' as const,   // Regular for inputs
    label: '500' as const,   // Medium for labels
  },
  secondary: {
    large: '400' as const,
    medium: '400' as const,
    small: '400' as const,
  },
  caption: {
    large: '400' as const,
    small: '400' as const,
  },
  // Emphasis variants
  emphasis: {
    bold: '700' as const,
    semibold: '600' as const,
    medium: '500' as const,
  },
} as const;

/**
 * TEXT STYLES (Preset combinations)
 *
 * Common text style presets matching Claude's patterns
 */
export const textStyles = {
  // Hero/Welcome (special screens)
  displayLarge: {
    fontSize: fontSize.display.large,
    lineHeight: lineHeight.display.large,
    fontWeight: fontWeight.display.large,
    letterSpacing: letterSpacing.display.large,
  },

  // Welcome prompts (Claude mobile real: ~32px)
  displayMedium: {
    fontSize: fontSize.display.medium,
    lineHeight: lineHeight.display.medium,
    fontWeight: fontWeight.display.medium,
    letterSpacing: letterSpacing.display.medium,
  },

  // Screen title
  h1: {
    fontSize: fontSize.heading.h1,
    lineHeight: lineHeight.heading.h1,
    fontWeight: fontWeight.heading.h1,
    letterSpacing: letterSpacing.heading.h1,
  },

  // Section heading
  h2: {
    fontSize: fontSize.heading.h2,
    lineHeight: lineHeight.heading.h2,
    fontWeight: fontWeight.heading.h2,
    letterSpacing: letterSpacing.heading.h2,
  },

  // Welcome prompt
  welcomePrompt: {
    fontSize: fontSize.heading.h3,
    lineHeight: lineHeight.heading.h3,
    fontWeight: fontWeight.heading.h3,
    letterSpacing: letterSpacing.heading.h3,
  },

  // Body text
  bodyLarge: {
    fontSize: fontSize.body.large,
    lineHeight: lineHeight.body.large,
    fontWeight: fontWeight.body.large,
    letterSpacing: letterSpacing.body.large,
  },

  bodyMedium: {
    fontSize: fontSize.body.medium,
    lineHeight: lineHeight.body.medium,
    fontWeight: fontWeight.body.medium,
    letterSpacing: letterSpacing.body.medium,
  },

  // Button text
  button: {
    fontSize: fontSize.ui.button,
    lineHeight: lineHeight.ui.button,
    fontWeight: fontWeight.ui.button,
    letterSpacing: letterSpacing.ui.button,
  },

  // Input placeholder/text
  input: {
    fontSize: fontSize.ui.input,
    lineHeight: lineHeight.ui.input,
    fontWeight: fontWeight.ui.input,
    letterSpacing: letterSpacing.ui.input,
  },

  // Secondary text (timestamps, metadata)
  secondary: {
    fontSize: fontSize.secondary.medium,
    lineHeight: lineHeight.secondary.medium,
    fontWeight: fontWeight.secondary.medium,
    letterSpacing: letterSpacing.secondary.medium,
  },

  // Caption text
  caption: {
    fontSize: fontSize.caption.large,
    lineHeight: lineHeight.caption.large,
    fontWeight: fontWeight.caption.large,
    letterSpacing: letterSpacing.caption.large,
  },
} as const;

/**
 * EXPORT UTILITIES
 */

// Get platform-specific font family
export const getPlatformFont = (type: 'ui' | 'display') => {
  const { Platform } = require('react-native');
  return Platform.OS === 'ios'
    ? fontFamilies[type].ios
    : fontFamilies[type].android;
};

// Export all typography tokens
export default {
  fontFamilies,
  fontSize,
  lineHeight,
  letterSpacing,
  fontWeight,
  textStyles,
  getPlatformFont,
} as const;
