/**
 * Salonier AI Spacing System
 * Inspired by Claude.ai interface design
 *
 * Spacing Philosophy:
 * - Generous whitespace for breathing room (<PERSON>'s signature)
 * - Consistent 4px/8px base scale for predictable rhythm
 * - Larger gaps between major sections
 * - Comfortable touch targets (min 44x44px on mobile)
 *
 * <PERSON>'s spacing patterns:
 * - Input field: Very generous padding (~300px height)
 * - Message bubbles: Comfortable spacing, not cramped
 * - Section gaps: Clear visual separation
 * - Border radius: Firmly rounded (24-36px common)
 */

/**
 * BASE SPACING SCALE
 * 8px base unit for consistent rhythm
 */
export const space = {
  /** 0px - No space */
  none: 0,

  /** 2px - Micro space (tight coupling) */
  xxs: 2,

  /** 4px - Extra small (minimal separation) */
  xs: 4,

  /** 8px - Small (compact spacing) */
  sm: 8,

  /** 12px - Medium-small (comfortable separation) */
  md: 12,

  /** 16px - Medium (standard spacing) - <PERSON> uses this heavily */
  lg: 16,

  /** 20px - Medium-large (section spacing) */
  xl: 20,

  /** 24px - Large (major sections) */
  xxl: 24,

  /** 32px - Extra large (clear separation) */
  xxxl: 32,

  /** 40px - Huge (major visual breaks) */
  huge: 40,

  /** 48px - Extra huge (screen-level separation) */
  xhuge: 48,

  /** 64px - Massive (hero sections) */
  massive: 64,
} as const;

/**
 * COMPONENT SPACING
 * Specific spacing for common UI patterns
 */
export const componentSpacing = {
  // Input/Composer (Claude mobile real measurements)
  composer: {
    /** Claude input height: ~110px mobile (match exacto capturas Claude real) */
    minHeight: 110,
    /** Internal padding */
    paddingHorizontal: 22,
    paddingVertical: 18,
    /** Gap between elements */
    gap: 12,
    /** Border width */
    borderWidth: 1.2,
  },

  // Message bubbles
  message: {
    /** Padding inside bubble */
    paddingHorizontal: 16,
    paddingVertical: 12,
    /** Gap between messages */
    gap: 12,
    /** Gap between message groups (different users) */
    groupGap: 20,
  },

  // Cards
  card: {
    /** Card internal padding */
    padding: 16,
    /** Gap between card elements */
    gap: 12,
    /** Gap between cards */
    marginBottom: 16,
  },

  // Buttons
  button: {
    /** Small button */
    small: {
      paddingHorizontal: 14,
      paddingVertical: 8,
      minHeight: 36,
    },
    /** Medium button (default) */
    medium: {
      paddingHorizontal: 18,
      paddingVertical: 11,
      minHeight: 44,
    },
    /** Large button */
    large: {
      paddingHorizontal: 24,
      paddingVertical: 14,
      minHeight: 52,
    },
    /** Icon-only button */
    icon: {
      width: 44,
      height: 44,
      padding: 10,
    },
  },

  // Lists
  list: {
    /** List item padding */
    itemPadding: 12,
    /** Gap between list items */
    itemGap: 8,
    /** List container padding */
    containerPadding: 20,
  },

  // Modals/Sheets
  modal: {
    /** Modal padding */
    padding: 20,
    /** Modal header padding */
    headerPadding: 16,
    /** Gap between modal sections */
    sectionGap: 24,
  },

  // Screen layout
  screen: {
    /** Standard horizontal padding */
    paddingHorizontal: 20,
    /** Standard vertical padding */
    paddingVertical: 16,
    /** Header padding top (includes safe area) */
    headerPaddingTop: 16,
    /** Header padding bottom */
    headerPaddingBottom: 16,
  },
} as const;

/**
 * BORDER RADIUS
 *
 * Claude uses generous, rounded corners throughout
 * - Input: 36px (firmly rounded)
 * - Buttons: 24-28px (pill-like)
 * - Cards: 16-20px (smooth)
 * - Bubbles: 24px (friendly)
 */
export const borderRadius = {
  /** No radius */
  none: 0,

  /** 4px - Subtle rounding */
  xs: 4,

  /** 8px - Small rounding */
  sm: 8,

  /** 12px - Medium rounding */
  md: 12,

  /** 16px - Standard rounding (cards) */
  lg: 16,

  /** 20px - Large rounding */
  xl: 20,

  /** 24px - Extra large (buttons, bubbles) */
  xxl: 24,

  /** 28px - Huge rounding */
  huge: 28,

  /** 32px - Massive rounding / Input rounding (Claude mobile: ~32px, menos redondeado que web) */
  massive: 32,
  input: 32,

  /** 50% - Full circle/pill */
  full: 9999,
} as const;

/**
 * TOUCH TARGETS
 *
 * Minimum sizes for comfortable mobile interaction
 * iOS HIG: 44x44pt minimum
 * Material Design: 48x48dp minimum
 * Claude: Generous touch targets throughout
 */
export const touchTarget = {
  /** Minimum safe touch target (44x44) */
  minimum: 44,

  /** Comfortable touch target (48x48) */
  comfortable: 48,

  /** Large touch target (52x52) */
  large: 52,

  /** Extra large touch target (56x56) */
  xlarge: 56,
} as const;

/**
 * SHADOWS
 *
 * Shadow elevation values (iOS/Android compatible)
 */
export const shadow = {
  /** No shadow */
  none: {
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },

  /** Small shadow (subtle depth) */
  sm: {
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.04,
    shadowRadius: 2,
    elevation: 1,
  },

  /** Medium shadow (cards) */
  md: {
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 4,
  },

  /** Large shadow (elevated elements) */
  lg: {
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 10,
    elevation: 4,
  },

  /** Extra large shadow (modals) */
  xl: {
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 8,
  },

  /** Primary shadow (buttons, accents) */
  primary: {
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
} as const;

/**
 * LAYOUT CONSTANTS
 *
 * Common layout values
 */
export const layout = {
  /** Maximum content width for readability */
  maxContentWidth: 680,

  /** Sidebar width (desktop) */
  sidebarWidth: 280,

  /** Header height (mobile) */
  headerHeight: 64,

  /** Tab bar height (mobile) */
  tabBarHeight: 80,

  /** Input composer base height (Claude mobile real: ~110px, match exacto capturas Claude real) */
  composerBaseHeight: 110,

  /** Avatar sizes */
  avatar: {
    small: 32,
    medium: 40,
    large: 48,
    xlarge: 64,
  },

  /** Icon sizes */
  icon: {
    small: 16,
    medium: 20,
    large: 24,
    xlarge: 32,
  },
} as const;

/**
 * ANIMATION DURATIONS
 *
 * Consistent animation timing
 */
export const duration = {
  /** Fast animation (100ms) - micro-interactions */
  fast: 100,

  /** Normal animation (200ms) - default */
  normal: 200,

  /** Slow animation (300ms) - page transitions */
  slow: 300,

  /** Extra slow animation (500ms) - complex transitions */
  xslow: 500,
} as const;

/**
 * EXPORT ALL
 */
export default {
  space,
  componentSpacing,
  borderRadius,
  touchTarget,
  shadow,
  layout,
  duration,
} as const;
