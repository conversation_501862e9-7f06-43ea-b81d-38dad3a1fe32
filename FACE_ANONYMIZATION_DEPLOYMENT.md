# Face Anonymization Edge Function - Quick Start

## Overview
Server-side face anonymization to prevent OpenAI VisionSafetyError rejections.

**Strategy**: Black out top 40% of images (where faces are) while preserving bottom 60% (hair color data).

## Quick Deploy

```bash
cd supabase/functions/anonymize-faces
./deploy.sh --production
```

## Files Created

```
supabase/functions/anonymize-faces/
├── index.ts          # Edge Function implementation (3 methods)
├── README.md         # Detailed API documentation
├── test.ts           # Test script for local/production testing
└── deploy.sh         # Deployment automation script

lib/
└── faceAnonymizer.ts # Updated client library (server-side enabled)

sessions/
└── 2025-11-04-face-anonymization-edge-function.md # Full documentation
```

## Methods Available

1. **Blackout** (Default) - Solid black bar, 100% effective
2. **Pixelation** - 40px blocks, very effective, better UX
3. **Blur** - 10x Gaussian passes, least effective, debug only

## Client Usage

No code changes needed! Already integrated via `AnonymizerContext`:

```typescript
// In step1.tsx, step2.tsx, chat.tsx
const { anonymizeImages } = useAnonymizer();

// Automatically uses Edge Function, falls back to local crop
const processedUris = await anonymizeImages(imageUris);
```

## Testing

### Local
```bash
./deploy.sh --test
```

### Production
1. Deploy: `./deploy.sh --production`
2. Test in app: Upload hair photo in step1.tsx
3. Check console: Should see "Server-side anonymization successful"

## Monitoring

```bash
# View Edge Function logs
bun x supabase functions logs anonymize-faces

# Check error rate in Supabase dashboard
# Navigate to: Edge Functions > anonymize-faces > Logs
```

## Expected Results

- **VisionSafetyError rate**: <5% (down from 30-40%)
- **Processing time**: <3 seconds for 3 images
- **Analysis quality**: No degradation

## Rollback

If issues occur:

```typescript
// In lib/faceAnonymizer.ts, line 41
// Uncomment to disable Edge Function:
const localResult = await localPrivacyFilter(imageUris);
return localResult;
```

## Full Documentation

See: `sessions/2025-11-04-face-anonymization-edge-function.md`

---

**Status**: Ready for deployment
**Review**: sessions/2025-11-04-face-anonymization-edge-function.md
