/**
 * Face Anonymization Module
 * Uses Supabase Edge Function for server-side face anonymization
 * Falls back to aggressive local filter if Edge Function fails
 */

import { supabase } from './supabase';
import * as FileSystem from 'expo-file-system/legacy';
import * as ImageManipulator from 'expo-image-manipulator';

interface AnonymizationResult {
  success: boolean;
  imageUris: string[];
  method: 'server' | 'local' | 'none';
  facesDetected?: number;
  error?: string;
}

/**
 * Anonymize faces in images using server-side Edge Function
 * @param imageUris Array of image URIs to process
 * @param method Anonymization method: 'crop' | 'blackout' | 'pixelate' | 'blur'
 * @returns Processed image URIs with faces anonymized
 */
export async function anonymizeFaces(
  imageUris: string[],
  method: 'crop' | 'blackout' | 'pixelate' | 'blur' = 'crop'
): Promise<AnonymizationResult> {
  try {
    // Skip if no images
    if (!imageUris || imageUris.length === 0) {
      return {
        success: true,
        imageUris: [],
        method: 'none'
      };
    }

    console.log(`Starting face anonymization for ${imageUris.length} images using method: ${method}`);

    // Try server-side anonymization first
    try {
      const result = await serverSideAnonymization(imageUris, method);
      if (result.success) {
        console.log(`Server-side anonymization successful (${result.method})`);
        return result;
      }
      console.warn('Server-side anonymization failed, falling back to local method');
    } catch (error) {
      console.error('Server anonymization error:', error);
      console.log('Falling back to local privacy filter');
    }

    // Fallback to local filter
    const localResult = await localPrivacyFilter(imageUris);
    return localResult;

  } catch (error) {
    console.error('Face anonymization failed:', error);
    // Return original images if all methods fail
    return {
      success: false,
      imageUris,
      method: 'none',
      error: error.message
    };
  }
}

/**
 * Server-side face anonymization using Edge Function
 */
async function serverSideAnonymization(
  imageUris: string[],
  method: 'crop' | 'blackout' | 'pixelate' | 'blur'
): Promise<AnonymizationResult> {
  try {
    // Convert images to base64
    const base64Images = await Promise.all(
      imageUris.map(async (uri) => {
        const base64 = await FileSystem.readAsStringAsync(uri, {
          encoding: FileSystem.EncodingType.Base64,
        });
        return base64;
      })
    );

    console.log(`Calling Edge Function with ${base64Images.length} images...`);

    // Call Edge Function with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    const { data, error } = await supabase.functions.invoke('anonymize-faces', {
      body: {
        images: base64Images,
        method,
        preserveRatio: 0.7 // Preserve bottom 70% for hair analysis (crop top 30%)
      }
    });

    clearTimeout(timeoutId);

    if (error) {
      throw error;
    }

    if (!data?.images) {
      throw new Error('Invalid response from anonymization service');
    }

    console.log(`Edge Function processed ${data.images.length} images`);

    // Convert base64 back to URIs
    // IMPORTANT: Edge Function returns JPEG images encoded as base64 strings
    // We need to decode them and write as binary files, NOT as base64 strings
    const processedUris = await Promise.all(
      data.images.map(async (base64: string, index: number) => {
        const timestamp = Date.now();
        const filename = `anonymized_${timestamp}_${index}.jpg`;
        const fileUri = FileSystem.documentDirectory + filename;

        // Write the base64 string directly (FileSystem will decode it automatically)
        await FileSystem.writeAsStringAsync(
          fileUri,
          base64,
          { encoding: FileSystem.EncodingType.Base64 }
        );

        return fileUri;
      })
    );

    return {
      success: true,
      imageUris: processedUris,
      method: 'server',
      facesDetected: data.facesDetected
    };

  } catch (error) {
    console.error('Server-side anonymization error:', error);
    throw error;
  }
}

/**
 * Local privacy filter fallback
 * Crops to bottom 70% of image where hair color information is
 * This removes faces (typically in top 30%) while preserving analysis data
 */
async function localPrivacyFilter(imageUris: string[]): Promise<AnonymizationResult> {
  try {
    console.log('Applying local privacy filter (bottom 70% crop)');

    const processedUris = await Promise.all(
      imageUris.map(async (uri) => {
        try {
          // Strategy: Crop to bottom 60% only
          // This is acceptable for hair color analysis since:
          // - Hair lengths are visible in bottom portion
          // - Color information is preserved
          // - Faces (top 40%) are completely removed
          
          // Get image info first
          const imageInfo = await FileSystem.getInfoAsync(uri);
          if (!imageInfo.exists) {
            throw new Error('Image file not found');
          }

          // Use expo-image-manipulator to crop
          // Default to standard portrait dimensions if we can't determine actual size
          const estimatedHeight = 1920; // Standard portrait height
          const cropStartY = Math.floor(estimatedHeight * 0.3); // Start at 30% down

          const result = await ImageManipulator.manipulateAsync(
            uri,
            [
              {
                crop: {
                  originX: 0,
                  originY: cropStartY,
                  width: 1080, // Standard portrait width
                  height: Math.floor(estimatedHeight * 0.7) // Bottom 70%
                }
              }
            ],
            {
              compress: 0.8,
              format: ImageManipulator.SaveFormat.JPEG
            }
          );

          console.log(`Cropped image to bottom 70%`);
          return result.uri;
          
        } catch (error) {
          console.error(`Failed to crop image: ${error}`);
          
          // Last resort: Extreme pixelation of entire image
          try {
            const result = await ImageManipulator.manipulateAsync(
              uri,
              [
                { resize: { width: 50 } }, // Very small
                { resize: { width: 800 } }, // Upscale (creates heavy pixelation)
              ],
              { compress: 0.2, format: ImageManipulator.SaveFormat.JPEG }
            );
            console.warn('Using extreme pixelation fallback');
            return result.uri;
          } catch {
            console.error('All processing methods failed, returning original');
            return uri;
          }
        }
      })
    );

    return {
      success: true,
      imageUris: processedUris,
      method: 'local'
    };

  } catch (error) {
    console.error('Local privacy filter error:', error);
    throw error;
  }
}

/**
 * Quick privacy check to determine if anonymization is needed
 * Can be used to skip processing for images without people
 */
export async function needsAnonymization(imageUri: string): Promise<boolean> {
  // For now, always return true for safety
  // In the future, could do quick local check for skin tones, face-like patterns
  return true;
}

/**
 * Cleanup temporary anonymized files
 */
export async function cleanupAnonymizedFiles(uris: string[]): Promise<void> {
  for (const uri of uris) {
    if (uri.includes('anonymized_')) {
      try {
        await FileSystem.deleteAsync(uri, { idempotent: true });
      } catch (error) {
        console.warn(`Failed to cleanup anonymized file: ${uri}`);
      }
    }
  }
}
