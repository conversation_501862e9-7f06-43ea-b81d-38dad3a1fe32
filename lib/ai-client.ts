/**
 * Cliente AI con soporte para OpenAI + Perplexity via Supabase Edge Functions
 * Manejo robusto de errores y reintentos
 */

import { Platform } from 'react-native';
import { supabase } from './supabase';
import { processMultipleImages } from './imageProcessor';
import * as FileSystem from 'expo-file-system/legacy';
import type { AppContext } from '@/types';

type UseCase = 'vision_analysis' | 'formula_generation' | 'product_search' | 'chat';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
const supabaseFunctionsBase = supabaseUrl
  ? supabaseUrl.replace(/\/$/, '').replace('.supabase.co', '.functions.supabase.co')
  : undefined;
const AI_PROXY_URL = supabaseFunctionsBase ? `${supabaseFunctionsBase}/ai-proxy` : undefined;

export type StreamEvent =
  | { type: 'token'; delta?: string; text: string }
  | { type: 'done'; text: string; usage?: { prompt_tokens?: number; completion_tokens?: number; total_tokens?: number }; cost?: number; latency?: number }
  | { type: 'error'; error: string };

interface AIProxyResponse {
  text: string;
  cost: number;
  latency: number;
  usage?: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
    cached?: boolean;
  };
  citations?: any[];
}

/**
 * Helper to read image as base64, handling both native URIs and web blob URLs
 */
async function readImageAsBase64(uri: string): Promise<string> {
  // En web, blob URLs necesitan ser leídos con fetch
  if (Platform.OS === 'web' && uri.startsWith('blob:')) {
    const response = await fetch(uri);
    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64 = reader.result as string;
        // Remover el prefijo "data:image/xxx;base64," si existe
        const base64Data = base64.split(',')[1] || base64;
        resolve(base64Data);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  // En nativo, usar FileSystem
  return FileSystem.readAsStringAsync(uri, {
    encoding: FileSystem.EncodingType.Base64,
  });
}

export interface GenerateTextOptions {
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string | Array<{ type: 'text'; text: string } | { type: 'image'; image: string }>;
  }>;
  maxRetries?: number;
  retryDelay?: number;
  useCase?: UseCase;
  imageUrls?: string[]; // Signed URLs from hair-photos (no upload needed)
  imageUris?: string[]; // Local URIs for workflow (formula step1/step2) - will be processed locally
  brand?: string;
  productLine?: string;
  temperature?: number;
  conversationHistory?: Array<{
    role: 'user' | 'assistant';
    content: string | Array<{ type: 'text'; text: string } | { type: 'image_url'; image_url: { url: string } }>;
  }>;
  stream?: boolean;
  onStreamResult?: (event: StreamEvent) => void;
  signal?: AbortSignal;
  requestTimeout?: number; // Timeout in milliseconds per request attempt
  appContext?: AppContext; // Context about where user is in the app
  skipThrottle?: boolean; // Internal: omits client-side throttle (for intent/memory mini-calls)
  imagesAlreadyProcessed?: boolean; // Indicates imageUris were already resized/compressed upstream
}

export class AIServiceError extends Error {
  constructor(
    message: string,
    public readonly originalError?: unknown,
    public readonly isRetryable: boolean = false
  ) {
    super(message);
    this.name = 'AIServiceError';
  }
}

/**
 * Client-side rate limiting to prevent AI request spam
 * Defaults to 0ms en dev para acelerar pruebas, 2000ms en prod (configurable vía EXPO_PUBLIC_AI_MIN_REQUEST_INTERVAL_MS).
 */
const envMinInterval = process.env.EXPO_PUBLIC_AI_MIN_REQUEST_INTERVAL_MS;
const parsedMinInterval = envMinInterval !== undefined ? Number(envMinInterval) : undefined;
const isDevEnvironment = typeof __DEV__ !== 'undefined' ? __DEV__ : process.env.NODE_ENV !== 'production';
const MIN_REQUEST_INTERVAL =
  parsedMinInterval !== undefined && !Number.isNaN(parsedMinInterval)
    ? Math.max(0, parsedMinInterval)
    : isDevEnvironment
      ? 0
      : 2000;

let lastRequestTime = 0;

async function throttleRequest(): Promise<void> {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
    const waitTime = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
    console.log(`[AIClient][Throttle] Rate limiting: waiting ${waitTime}ms before next request`);
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }

  lastRequestTime = Date.now();
}


export class VisionSafetyError extends AIServiceError {
  constructor(message: string) {
    super(message, undefined, true); // retryable = true
    this.name = 'VisionSafetyError';
  }
}
class StreamingNotSupportedError extends AIServiceError {
  constructor(message: string) {
    super(message, undefined, false);
    this.name = 'StreamingNotSupportedError';
  }
}

/**
 * Función principal para generar texto con IA
 */
interface PreparedPayload {
  systemPrompt: string;
  userPrompt: string;
  finalImageUrls?: string[];
}

async function prepareAIRequestPayload(options: GenerateTextOptions & { useCase: UseCase }): Promise<PreparedPayload> {
  let systemPrompt = '';
  let userPrompt = '';

  for (const msg of options.messages) {
    const content = typeof msg.content === 'string'
      ? msg.content
      : msg.content
          .filter(p => p.type === 'text')
          .map((p: any) => p.text)
          .join('\n\n');

    if (msg.role === 'system') {
      systemPrompt = content;
    } else if (msg.role === 'user') {
      userPrompt += content + '\n\n';
    }
  }

  let finalImageUrls: string[] | undefined;

  if (options.imageUrls && options.imageUrls.length > 0) {
    finalImageUrls = options.imageUrls;
    console.log(`[AIClient] Using ${options.imageUrls.length} pre-uploaded images from hair-photos`);
  } else if (options.imageUris && options.imageUris.length > 0 && options.useCase === 'vision_analysis') {
    const shouldProcess = !options.imagesAlreadyProcessed;
    const sourceUris = options.imageUris;

    if (shouldProcess) {
      console.log(`[AIClient] Processing ${sourceUris.length} local images...`);
    } else {
      console.log(`[AIClient] Using ${sourceUris.length} pre-processed local images`);
    }

    try {
      const processedUris = shouldProcess ? await processMultipleImages(sourceUris) : sourceUris;
      const dataUrls: string[] = [];

      for (let i = 0; i < processedUris.length; i++) {
        const processedUri = processedUris[i];
        try {
          console.log(`[AIClient] Reading image ${i + 1}/${processedUris.length}: ${processedUri}`);
          const base64 = await readImageAsBase64(processedUri);

          if (!base64 || base64.trim().length === 0) {
            console.error(`[AIClient] Image ${i + 1} returned empty base64`);
            throw new Error(`La imagen ${i + 1} no se pudo leer correctamente`);
          }

          dataUrls.push(`data:image/jpeg;base64,${base64}`);
          console.log(`[AIClient] Successfully processed image ${i + 1}, base64 length: ${base64.length}`);
        } catch (imageError: any) {
          console.error(`[AIClient] Failed to read image ${i + 1}:`, imageError);
          throw new AIServiceError(
            `No se pudo analizar la imagen ${i + 1}.\n\nVerifica que la imagen sea válida e intenta nuevamente.`,
            imageError,
            false
          );
        }
      }

      if (dataUrls.length === 0) {
        throw new AIServiceError(
          'No se pudo procesar ninguna imagen.\n\nPor favor, intenta seleccionar las imágenes nuevamente.',
          undefined,
          false
        );
      }

      finalImageUrls = dataUrls;
      console.log(`[AIClient] Successfully processed ${dataUrls.length} images as data URLs`);
    } catch (processingError: any) {
      console.error('[AIClient] Error during image processing:', processingError);

      // Si ya es un AIServiceError, re-throw
      if (processingError instanceof AIServiceError) {
        throw processingError;
      }

      // Si no, crear un nuevo AIServiceError con mensaje específico
      throw new AIServiceError(
        'No se pudieron procesar las imágenes.\n\nVerifica que las imágenes sean válidas e intenta nuevamente.',
        processingError,
        false
      );
    }
  }

  return {
    systemPrompt,
    userPrompt: userPrompt.trim(),
    finalImageUrls,
  };
}

function createBaseRequestBody(
  options: GenerateTextOptions & { useCase: UseCase },
  payload: PreparedPayload
) {
  const body: Record<string, unknown> = {
    useCase: options.useCase || 'chat',
    prompt: payload.userPrompt,
  };

  if (payload.systemPrompt) {
    body.systemPrompt = payload.systemPrompt;
  }

  if (payload.finalImageUrls && payload.finalImageUrls.length > 0) {
    body.imageUrls = payload.finalImageUrls;
  }

  if (options.brand) {
    body.brand = options.brand;
  }

  if (options.productLine) {
    body.productLine = options.productLine;
  }

  if (typeof options.temperature === 'number') {
    body.temperature = options.temperature;
  }

  if (options.conversationHistory && options.conversationHistory.length > 0) {
    body.conversationHistory = options.conversationHistory;
  }

  if (options.appContext) {
    body.appContext = options.appContext;
    console.log('[ai-client] 🚀 Including appContext in request:', JSON.stringify(options.appContext, null, 2));
  } else {
    console.log('[ai-client] ⚠️ No appContext provided in options');
  }

  return body;
}

function parseSSEEvent(chunk: string): { event: string; data?: any } | null {
  const lines = chunk.split('\n');
  let eventName = 'token';
  let dataPayload = '';

  for (const rawLine of lines) {
    const line = rawLine.trimEnd();
    if (!line || line.startsWith(':')) {
      continue;
    }

    if (line.startsWith('event:')) {
      eventName = line.slice(6).trim();
    } else if (line.startsWith('data:')) {
      const value = line.slice(5).trimStart();
      dataPayload += dataPayload ? `\n${value}` : value;
    }
  }

  if (!dataPayload) {
    return null;
  }

  try {
    const data = JSON.parse(dataPayload);
    return { event: eventName, data };
  } catch (error) {
    console.warn('[AIClient] Failed to parse SSE data payload:', error);
    return null;
  }
}

async function streamAiProxy(
  options: GenerateTextOptions & { useCase: UseCase },
  payload: PreparedPayload
): Promise<string> {
  if (!AI_PROXY_URL || !supabaseAnonKey) {
    throw new AIServiceError('Supabase Functions no está configurado correctamente.', undefined, false);
  }

  const { data: session } = await supabase.auth.getSession();
  if (!session.session) {
    throw new AIServiceError('Tu sesión ha expirado. Por favor, inicia sesión nuevamente.', undefined, false);
  }

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    apikey: supabaseAnonKey,
    Authorization: `Bearer ${session.session.access_token}`,
  };

  const requestBody = {
    ...createBaseRequestBody(options, payload),
    stream: true,
  };

  console.log(`[AIClient] Starting streaming request - Use case: ${options.useCase}`);

  let response: Response;
  try {
    response = await fetch(AI_PROXY_URL, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody),
      signal: options.signal,
    });
  } catch (error: any) {
    console.error('[AIClient] Streaming fetch failed:', error);
    if (error?.message?.includes('Network request failed') || error?.name === 'TypeError') {
      throw new StreamingNotSupportedError('Streaming no soportado en esta plataforma.');
    }
    throw new AIServiceError('No se pudo iniciar el streaming con el servidor.', error, true);
  }

  if (!response.ok) {
    let errorMessage = `Edge Function streaming error (${response.status})`;
    try {
      const text = await response.text();
      if (text) {
        const parsed = JSON.parse(text);
        if (parsed?.error === 'stream_not_supported') {
          throw new StreamingNotSupportedError(parsed.message || 'Streaming no soportado para este caso de uso.');
        }
        errorMessage = parsed?.message || errorMessage;
      }
    } catch (parseError) {
      // Ignore JSON parse errors, keep default message
    }

    console.error('[AIClient] Streaming response error:', errorMessage);
    if (response.status === 504) {
      throw new StreamingNotSupportedError(errorMessage);
    }
    throw new AIServiceError(errorMessage, undefined, response.status >= 500);
  }

  if (!response.body || typeof (response.body as any).getReader !== 'function') {
    throw new StreamingNotSupportedError('La plataforma actual no soporta streams nativos.');
  }

  const reader = (response.body as ReadableStream<Uint8Array>).getReader();
  const decoder = new TextDecoder('utf-8');

  let buffer = '';
  let finalText = '';
  let doneReceived = false;

  const flushBuffer = (isLastChunk = false) => {
    let boundaryIndex = buffer.indexOf('\n\n');
    while (boundaryIndex !== -1) {
      const rawEvent = buffer.slice(0, boundaryIndex);
      buffer = buffer.slice(boundaryIndex + 2);

      const parsed = parseSSEEvent(rawEvent);
      if (!parsed) {
        boundaryIndex = buffer.indexOf('\n\n');
        continue;
      }

      const { event, data } = parsed;

      if (event === 'token') {
        const nextText = typeof data?.text === 'string'
          ? data.text
          : data?.delta
            ? finalText + data.delta
            : finalText;

        finalText = nextText;
        options.onStreamResult?.({
          type: 'token',
          delta: data?.delta,
          text: finalText,
        });
      } else if (event === 'done') {
        const completeText = typeof data?.text === 'string' ? data.text : finalText;
        finalText = completeText;
        doneReceived = true;
        options.onStreamResult?.({
          type: 'done',
          text: completeText,
          usage: data?.usage,
          cost: data?.cost,
          latency: data?.latency,
        });
      } else if (event === 'error') {
        const errorMessage = data?.message || 'Error durante el streaming.';
        options.onStreamResult?.({
          type: 'error',
          error: errorMessage,
        });
        throw new AIServiceError(errorMessage, undefined, false);
      }

      boundaryIndex = buffer.indexOf('\n\n');
    }

    if (isLastChunk && buffer.trim().length > 0) {
      const parsed = parseSSEEvent(buffer);
      if (parsed) {
        buffer = '';
        const { event, data } = parsed;
        if (event === 'token') {
          const nextText = typeof data?.text === 'string'
            ? data.text
            : data?.delta
              ? finalText + data.delta
              : finalText;
          finalText = nextText;
          options.onStreamResult?.({
            type: 'token',
            delta: data?.delta,
            text: finalText,
          });
        } else if (event === 'done') {
          const completeText = typeof data?.text === 'string' ? data.text : finalText;
          finalText = completeText;
          doneReceived = true;
          options.onStreamResult?.({
            type: 'done',
            text: completeText,
            usage: data?.usage,
            cost: data?.cost,
            latency: data?.latency,
          });
        }
      }
    }
  };

  try {
    while (true) {
      const { value, done } = await reader.read();
      if (done) {
        buffer += decoder.decode();
        flushBuffer(true);
        break;
      }

      buffer += decoder.decode(value, { stream: true });
      flushBuffer();
    }
  } finally {
    reader.releaseLock?.();
  }

  if (!doneReceived) {
    options.onStreamResult?.({
      type: 'done',
      text: finalText,
    });
  }

  return finalText;
}

function isRetryableError(error: any): boolean {
  if (error instanceof AIServiceError) {
    return error.isRetryable;
  }

  const message = error?.message || '';
  if (message.includes('rate_limit')) return false;
  if (message.includes('authenticated') || message.includes('Session')) return false;
  if (message.includes('Aborted') || message.includes('abort')) return false; // ✅ Abort errors are NOT retryable
  if (message.includes('Network')) return true;
  if (error?.status === 429) return false;
  if (typeof error?.status === 'number' && error.status >= 500) return true;

  return false;
}

async function invokeAiProxy(
  body: Record<string, unknown>,
  signal?: AbortSignal
): Promise<AIProxyResponse> {
  if (AI_PROXY_URL && supabaseAnonKey) {
    const { data: session } = await supabase.auth.getSession();
    if (!session.session) {
      throw new AIServiceError('Tu sesión ha expirado. Por favor, inicia sesión nuevamente.', undefined, false);
    }

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      apikey: supabaseAnonKey,
      Authorization: `Bearer ${session.session.access_token}`,
    };

    try {
      const response = await fetch(AI_PROXY_URL, {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
        signal,
      });

      if (!response.ok) {
        let errorPayload: any = null;
        let errorText = '';
        try {
          // ✅ FIX: Read as text first, then try to parse as JSON
          errorText = await response.text();
          errorPayload = JSON.parse(errorText);
        } catch {
          // If JSON parse fails, use the text as-is
          errorPayload = errorText;
        }


        // Detect vision safety rejection
        if (errorPayload?.error === 'vision_safety_rejection') {
          throw new VisionSafetyError(
            errorPayload.message || 
            'No pudimos analizar las imágenes debido a restricciones de seguridad.'
          );
        }

        const message =
          (errorPayload && typeof errorPayload === 'object' && errorPayload.message)
            ? errorPayload.message
            : `Edge Function error (${response.status})`;

        throw new AIServiceError(message, errorPayload, response.status >= 500);
      }

      const buffer = await response.arrayBuffer();
      const bodyText = new TextDecoder('utf-8').decode(buffer);

      if (!bodyText) {
        throw new AIServiceError('Edge Function returned no data', undefined, true);
      }

      const data = JSON.parse(bodyText);
      if (!data) {
        throw new AIServiceError('Edge Function returned no data', undefined, true);
      }

      return data as AIProxyResponse;
    } catch (error: any) {
      if (error instanceof AIServiceError) {
        throw error;
      }

      const message = error?.message || 'No se pudo comunicarse con el servicio de IA.';
      const isRetryable =
        error?.name === 'AbortError' ||
        message.includes('Network request failed') ||
        message.includes('Failed to fetch') ||
        message.includes('Network');

      throw new AIServiceError(message, error, isRetryable);
    }
  }

  const response = await supabase.functions.invoke('ai-proxy', {
    body,
  });

  if (response.error) {
    let errorMessage = 'Edge Function error';
    try {
      if (response.error.message) {
        errorMessage = response.error.message;
      } else if (response.error.context?.status) {
        errorMessage = `Edge Function error (${response.error.context.status})`;
      }
    } catch {
      // ignore
    }

    throw new AIServiceError(errorMessage, response.error, true);
  }

  if (!response.data) {
    throw new AIServiceError('Edge Function returned no data', undefined, true);
  }

  return response.data as AIProxyResponse;
}

export async function generateTextSafe(options: GenerateTextOptions): Promise<string> {
  // Client-side rate limiting (1 request per 2 seconds)
  const { skipThrottle = false } = options;

  if (!skipThrottle) {
    await throttleRequest();
  }

  const {
    maxRetries = 2,
    retryDelay = 1500,
    useCase = 'chat',
    stream = false,
    requestTimeout,
  } = options;

  const normalizedOptions = { ...options, useCase } as GenerateTextOptions & { useCase: UseCase };

  // ✅ Prepare payload ONCE (includes image processing) to avoid re-reading files on retry
  const payload = await prepareAIRequestPayload(normalizedOptions);

  if (stream) {
    try {
      const streamedText = await streamAiProxy(normalizedOptions, payload);
      if (!streamedText.trim()) {
        console.warn('[AIClient] Streaming returned empty text, retrying without stream.');
        const fallbackText = await generateTextSafe({ ...options, stream: false });
        options.onStreamResult?.({
          type: 'done',
          text: fallbackText,
        });
        return fallbackText;
      }
      return streamedText;
    } catch (error) {
      if (error instanceof StreamingNotSupportedError) {
        console.warn('[AIClient] Streaming not supported, falling back to buffered response.');
        const fallbackText = await generateTextSafe({ ...options, stream: false });
        options.onStreamResult?.({
          type: 'done',
          text: fallbackText,
        });
        return fallbackText;
      }
      throw error;
    }
  }

  let lastError: unknown;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    // ✅ Create new AbortController for EACH attempt
    const abortController = new AbortController();
    let timeoutId: ReturnType<typeof setTimeout> | undefined;

    // Fix #6: Chain external abort signal (component unmount) with internal timeout signal
    if (options.signal) {
      if (options.signal.aborted) {
        // Already aborted, throw immediately
        throw new AIServiceError('Request was aborted by component unmount', undefined, false);
      }
      // Listen to external signal and abort internal controller
      options.signal.addEventListener('abort', () => {
        console.log('[AIClient] External abort signal received (component unmount)');
        abortController.abort();
      }, { once: true });
    }

    if (requestTimeout) {
      timeoutId = setTimeout(() => {
        console.warn(`[AIClient] Request timeout after ${requestTimeout}ms (attempt ${attempt + 1})`);
        abortController.abort();
      }, requestTimeout);
    }

    try {
      console.log(`[AIClient] Attempt ${attempt + 1}/${maxRetries + 1} - Use case: ${useCase}`);

      // ✅ Create fresh request body for EACH attempt (but reuse cached payload)
      const requestBody = createBaseRequestBody(normalizedOptions, payload);
      const functionResponse = await invokeAiProxy(requestBody, abortController.signal);

      const aiText = typeof functionResponse.text === 'string' ? functionResponse.text : '';
      if (!aiText.trim()) {
        throw new AIServiceError(
          'La IA respondió sin contenido. Reintentando...',
          undefined,
          true
        );
      }

      if (timeoutId) clearTimeout(timeoutId);

      console.log(`[AIClient] Success! Cost: $${functionResponse.cost?.toFixed?.(4) || '0.0000'}, Latency: ${functionResponse.latency}ms`);

      return functionResponse.text;
    } catch (error: any) {
      if (timeoutId) clearTimeout(timeoutId);

      lastError = error;
      console.error(`[AIClient] Attempt ${attempt + 1} failed:`, error);

      const shouldRetry = attempt < maxRetries && isRetryableError(error);

      if (shouldRetry) {
        const delay = retryDelay * (attempt + 1);
        console.log(`[AIClient] Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }

      if (error instanceof AIServiceError) {
        throw error;
      }

      if (error?.message?.includes('rate_limit')) {
        throw new AIServiceError(
          'Has excedido el límite de uso. Por favor, intenta más tarde.',
          error,
          false
        );
      }

      if (error?.message?.includes('authenticated') || error?.message?.includes('Session')) {
        throw new AIServiceError(
          'Tu sesión ha expirado. Por favor, inicia sesión nuevamente.',
          error,
          false
        );
      }

      if (error?.message?.includes('Aborted') || error?.name === 'AbortError') {
        throw new AIServiceError(
          'La generación de fórmula está tardando demasiado. Por favor, intenta con una transformación más simple o revisa los datos ingresados.',
          error,
          false
        );
      }

      throw new AIServiceError(
        'Error al comunicarse con el servicio de IA. Por favor, intenta nuevamente.',
        error,
        false
      );
    }
  }

  console.error('[AIClient] All retries exhausted');
  throw new AIServiceError(
    'El servicio de IA no está disponible en este momento. Por favor, intenta en unos minutos.',
    lastError,
    false
  );
}

/**
 * Función específica para búsqueda de productos con Perplexity
 */
export async function searchProducts(
  brand: string,
  productLine: string | undefined,
  query: string
): Promise<{ content: string; citations: any[] }> {
  console.log(`[AIClient] Searching products: ${brand} ${productLine || ''}`);

  const { data: session } = await supabase.auth.getSession();
  if (!session.session) {
    throw new AIServiceError('Session expired', undefined, false);
  }

  const response = await invokeAiProxy(
    {
      useCase: 'product_search',
      prompt: query,
      brand,
      productLine,
    }
  );

  console.log(`[AIClient] Product search success! Cost: $${response.cost?.toFixed?.(4) || '0.0000'}`);

  return {
    content: response.text,
    citations: response.citations || [],
  };
}

/**
 * Helper para obtener mensaje de error user-friendly
 */
export function getErrorMessage(error: unknown): string {
  if (error instanceof AIServiceError) {
    return error.message;
  }

  if (error instanceof Error) {
    if (error.message.includes('Rate limit') || error.message.includes('rate_limit')) {
      return 'Has excedido el límite de uso.\n\nPor favor, intenta más tarde o actualiza tu plan.';
    }

    if (error.message.includes('authenticated') || error.message.includes('Session')) {
      return 'Tu sesión ha expirado.\n\nPor favor, inicia sesión nuevamente.';
    }

    if (error.message.includes('Network') || error.message.includes('fetch')) {
      return 'Error de conexión.\n\nVerifica tu conexión a internet e intenta nuevamente.';
    }
  }

  return 'Lo siento, hubo un error inesperado.\n\nPor favor, intenta de nuevo.';
}
