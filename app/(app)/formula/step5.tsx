/**
 * Step 5: Formula Generation & Chat
 * Refactored version with chat-style mentor U<PERSON>
 */

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import type { MutableRefObject } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Modal,
  Image,
  Alert,
  Share,
  Clipboard,
  Keyboard,
  KeyboardEvent,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams } from 'expo-router';
import {
  Save,
  Share2,
  Copy,
  DollarSign,
  RefreshCw,
  ImageIcon,
  Camera,
  X,
  Check,
} from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import Colors from '@/constants/colors';
import { textStyles } from '@/constants/typography';
import { space, layout as layoutConstants, borderRadius, shadow } from '@/constants/spacing';
import { useFormula } from '@/contexts/FormulaContext';
import { useClients } from '@/contexts/ClientContext';
import { generateTextSafe, getErrorMessage } from '@/lib/ai-client';
import { uploadFormulaPhoto } from '@/lib/storage';
import { supabase } from '@/lib/supabase';
import type { ConversationIntent, ConversationMemorySnapshot, Message } from '@/types';
import { buildFormulaContext, sanitizeContext } from '@/lib/context-builders';
import {
  detectConversationIntent,
  describeIntent,
  formatMemoryForPrompt,
  summarizeConversation,
} from '@/lib/conversation-intelligence';
import type { IntentDetectionResult } from '@/lib/conversation-intelligence';
import FormattedMessageContent from '@/components/chat/FormattedMessageContent';
import ProgressIndicator from '@/components/ProgressIndicator';
import {
  getFormulaSystemPrompt,
  getFormulaUserPrompt,
  getChatSystemPrompt,
  getQuickQuestions,
  type FormulaPromptContext,
} from '@/lib/formula-prompts';
import {
  saveFormula,
  extractProductsFromText,
  getLatestSessionNumber,
  estimateCost,
} from '@/lib/supabase-formulas';
import {
  FORMULA_GENERATION_TIMEOUT_MS,
  CHAT_TIMEOUT_MS,
  MAX_CONVERSATION_CONTEXT_MESSAGES,
} from '@/constants/timing';
import ChatWelcomeHero from '@/components/chat/ChatWelcomeHero';
import ChatComposer from '@/components/chat/ChatComposer';
import MessageBubble from '@/components/chat/MessageBubble';
import { useImageAttachments } from '@/hooks/useImageAttachments';
import { processMultipleImages } from '@/lib/imageProcessor';
import { useAnonymizer } from '@/contexts/AnonymizerContext';

type GenerationStage = 'analysis' | 'validation' | 'references' | 'drafting' | 'done';

// Scroll Anchoring Constants
const CHAT_CONTAINER_TOP_MARGIN = 24; // Margin above anchored message in chat container
const KEYBOARD_SCROLL_BOTTOM_PADDING = 36; // Extra padding when keyboard is visible
const DEFAULT_SCROLL_BOTTOM_PADDING = 120; // Default bottom padding when keyboard is hidden

export const computeStep5AnchorTarget = (containerOffset: number, layoutY: number) =>
  Math.max(containerOffset + layoutY - CHAT_CONTAINER_TOP_MARGIN, 0);

type Step5Scrollable = {
  scrollToIndex?: (options: {
    index: number;
    animated?: boolean;
    viewPosition?: number;
    viewOffset?: number;
  }) => void;
  scrollToOffset?: (options: { offset: number; animated?: boolean }) => void;
  scrollTo?: (options: { x?: number; y?: number; animated?: boolean }) => void;
};

export interface Step5AnchorHandlerConfig {
  pendingAnchorIdRef: MutableRefObject<string | null>;
  chatContainerOffsetRef: MutableRefObject<number>;
  scrollableRef: MutableRefObject<Step5Scrollable | null>;
}

export const createStep5AnchorHandler = ({
  pendingAnchorIdRef,
  chatContainerOffsetRef,
  scrollableRef,
}: Step5AnchorHandlerConfig) => (
  messageId: string,
  layoutY: number,
  params?: { index?: number }
) => {
  if (pendingAnchorIdRef.current !== messageId) return;

  const containerOffset = chatContainerOffsetRef.current;
  const scrollable = scrollableRef.current;
  const index = params?.index;
  const hasMeasuredHeader = containerOffset > 0;

  const performScrollToOffset = () => {
    const targetY = computeStep5AnchorTarget(containerOffset, layoutY);
    if (scrollable?.scrollToOffset) {
      scrollable.scrollToOffset({ offset: targetY, animated: true });
      return true;
    }
    if (scrollable?.scrollTo) {
      scrollable.scrollTo({ y: targetY, animated: true });
      return true;
    }
    return false;
  };

  const performScrollToIndex = () => {
    if (scrollable?.scrollToIndex == null || typeof index !== 'number') {
      return false;
    }
    try {
      scrollable.scrollToIndex({
        index,
        animated: true,
        viewPosition: 0,
        viewOffset: hasMeasuredHeader ? CHAT_CONTAINER_TOP_MARGIN : 0,
      });
      return true;
    } catch (error) {
      if (__DEV__) {
        console.warn('[Step5Anchor] scrollToIndex failed', { messageId, index, error });
      }
      return false;
    }
  };

  requestAnimationFrame(() => {
    const anchoredViaIndex = performScrollToIndex();
    if (!anchoredViaIndex) {
      performScrollToOffset();
    }
  });

  pendingAnchorIdRef.current = null;
};

export default function Step5ChatScreen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const { clientId } = useLocalSearchParams<{ clientId: string }>();
  const { clients } = useClients();
  const { formulaData, selectedClient, selectClient, resetFormula, isLoaded } = useFormula();
  const { anonymizeImages } = useAnonymizer();
  const flatListRef = useRef<FlatList<Message>>(null);
  const mixingRatioCacheRef = useRef(new Map<string, string>());
  const keyboardHeightRef = useRef(0);
  const chatContainerOffsetRef = useRef(0);
  const pendingAnchorIdRef = useRef<string | null>(null);
  const shouldAutoScrollRef = useRef(false);
  // Restore client from route params if context is empty
  useEffect(() => {
    if (clientId && !selectedClient) {
      const client = clients.find(c => c.id === clientId);
      if (client) {
        selectClient(client);
        console.log('[Step5] Cliente restaurado desde route params:', client.name);
      } else {
        console.warn('[Step5] Cliente no encontrado en lista:', clientId);
      }
    } else if (!clientId) {
      console.warn('[Step5] No hay clientId en route params');
    }
  }, [clientId, selectedClient, clients, selectClient]);

  const [keyboardVisible, setKeyboardVisible] = useState(false);

  useEffect(() => {
    const showListener = Keyboard.addListener('keyboardDidShow', (event: KeyboardEvent) => {
      keyboardHeightRef.current = event.endCoordinates?.height ?? 0;
      setKeyboardVisible(true);
    });
    const hideListener = Keyboard.addListener('keyboardDidHide', () => {
      keyboardHeightRef.current = 0;
      setKeyboardVisible(false);

      // Mark that we should auto-scroll when content size changes
      // This ensures scroll happens AFTER keyboard animation completes AND FlatList finishes rendering
      shouldAutoScrollRef.current = true;
    });

    return () => {
      showListener.remove();
      hideListener.remove();
    };
  }, []);

  const currentAnalysis = formulaData.currentColorAnalysis;
  const desiredAnalysis = formulaData.desiredColorAnalysis;
  const brand = formulaData.brand;
  const productLine = formulaData.productLine;
  const clientName = formulaData.clientName;
  const technique = formulaData.technique;

  // Check if we have all required data
  const hasRequiredData = currentAnalysis && desiredAnalysis && brand;

  // State
  const [formula, setFormula] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(true);
  const [generationStage, setGenerationStage] = useState<GenerationStage>('analysis');
  const [chatMessages, setChatMessages] = useState<Message[]>([]);
  const [memorySnapshot, setMemorySnapshot] = useState<ConversationMemorySnapshot | null>(null);
  const [inputText, setInputText] = useState('');
  const {
    selectedImages,
    addImages,
    removeImageAt,
    clearImages,
    showAttachmentOptions,
    toggleAttachmentOptions,
    hideAttachmentOptions,
  } = useImageAttachments();
  const [isSending, setIsSending] = useState(false);
  const [isUploadingPhotos, setIsUploadingPhotos] = useState(false);

  // Modals
  const [showPhotoPreview, setShowPhotoPreview] = useState(false);
  const [showBrandPicker, setShowBrandPicker] = useState(false);

  // Alternative brands for switching
  const alternativeBrands = [
    "L'Oréal Professionnel INOA",
    'Schwarzkopf Igora Royal',
    'Redken Shades EQ',
    'Matrix SoColor',
    'Wella Koleston Perfect',
  ];

  // All photos from workflow
  const allPhotos = [
    ...(formulaData.currentColorImages || []),
    ...(formulaData.desiredColorImages || []),
  ];

  // Prompt context
  const levelDifference = currentAnalysis && desiredAnalysis
    ? Math.abs(desiredAnalysis.level - currentAnalysis.roots.level)
    : 0;

  const needsMultipleSessions =
    levelDifference > 3 ||
    currentAnalysis?.roots.state === 'dañado' ||
    currentAnalysis?.roots.state === 'muy dañado' ||
    (currentAnalysis?.chemicalHistory.lastProcessType !== 'ninguno' && levelDifference > 2);

  const promptContext: FormulaPromptContext | null =
    currentAnalysis && desiredAnalysis && brand
      ? {
          brand,
          productLine,
          currentAnalysis,
          desiredAnalysis,
          clientName,
          technique,
          mixingRatioInfo: undefined,
          levelDifference,
          needsMultipleSessions,
        }
      : null;

  // Quick questions
  const quickQuestions = promptContext ? getQuickQuestions(promptContext) : [];

  // Use layout constant from spacing system (Claude mobile real: 110px)
  const composerBaseHeight = layoutConstants.composerBaseHeight;
  const scrollBottomPadding = keyboardVisible
    ? keyboardHeightRef.current + composerBaseHeight + KEYBOARD_SCROLL_BOTTOM_PADDING
    : DEFAULT_SCROLL_BOTTOM_PADDING;
  // Claude mobile: composer pegado abajo sin espacio extra cuando teclado oculto
  const inputBottomPadding = keyboardVisible ? space.xs + 2 : insets.bottom || 0;

  /**
   * Scroll Anchoring for the Step 5 FlatList (simpler than the full chat screen)
   *
   * Flow:
   * 1. User sends message → Mark message ID as "pending anchor"
   * 2. Message renders → onLayout callback provides layout.y position
   * 3. Calculate target scroll position: chatContainerOffset + layoutY - topMargin
   * 4. Scroll to calculated position
   */
  const anchorMessagePosition = useMemo(
    () =>
      createStep5AnchorHandler({
        pendingAnchorIdRef,
        chatContainerOffsetRef,
        scrollableRef: flatListRef,
      }),
    [pendingAnchorIdRef, chatContainerOffsetRef, flatListRef]
  );

  // =====================================================
  // FORMULA GENERATION
  // =====================================================

  const verifyMixingRatio = async (
    brand: string,
    productLine?: string
  ): Promise<string> => {
    const cacheKey = `${brand}-${productLine || 'default'}`;

    if (mixingRatioCacheRef.current.has(cacheKey)) {
      return mixingRatioCacheRef.current.get(cacheKey)!;
    }

    try {
      const query = `Busca y extrae ÚNICAMENTE la proporción de mezcla oficial de ${brand}${
        productLine ? ` línea ${productLine}` : ''
      }.

Responde SOLO con el formato exacto:
"Color X:Y Oxidante" o "No requiere oxidante" o "Consultar manual técnico"

Ejemplos:
- "1:1.5 (60g color + 90ml oxidante)"
- "1:2 (para esta línea específica)"
- "No requiere oxidante (tono directo)"

Solo información verificada de fuentes oficiales del fabricante.`;

      const verification = await generateTextSafe({
        messages: [{ role: 'user', content: query }],
        useCase: 'product_search',
        maxRetries: 1,
      });

      console.log(`[Step5Chat] Mixing ratio verified for ${brand} ${productLine || ''}`);
      mixingRatioCacheRef.current.set(cacheKey, verification);
      return verification;
    } catch (error) {
      console.warn('[Step5Chat] Mixing ratio verification failed:', error);
      return 'Consultar manual técnico del fabricante';
    }
  };

  const generateFormula = useCallback(async () => {
    setIsGenerating(true);
    setGenerationStage('analysis');

    try {
      if (!currentAnalysis || !desiredAnalysis || !brand) {
        throw new Error('Faltan datos necesarios para generar la fórmula');
      }

      setGenerationStage('validation');

      // Verify mixing ratios
      setGenerationStage('references');
      let mixingRatioInfo = '';

      if (brand && brand !== 'Sin marca específica') {
        console.log('[Step5Chat] Verifying mixing ratios...');
        try {
          mixingRatioInfo = await verifyMixingRatio(brand, productLine);
        } catch (error) {
          console.warn('[Step5Chat] Mixing ratio verification failed:', error);
        }
      }

      setGenerationStage('drafting');
      setFormula('');

      const context: FormulaPromptContext = {
        brand: brand || 'Sin marca específica',
        productLine,
        currentAnalysis,
        desiredAnalysis,
        clientName,
        technique,
        mixingRatioInfo,
        levelDifference,
        needsMultipleSessions,
      };

      const systemPrompt = getFormulaSystemPrompt(context);
      const userPrompt = getFormulaUserPrompt(context);

      console.log('[Step5Chat] Generating formula...');

      let streamStarted = false;
      let streamedFormula = '';

      const baseFormula = await generateTextSafe({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt },
        ],
        maxRetries: 2,
        retryDelay: 1500,
        useCase: 'formula_generation',
        stream: true,
        requestTimeout: FORMULA_GENERATION_TIMEOUT_MS,
        onStreamResult: (event) => {
          if (
            (event.type === 'token' || event.type === 'done') &&
            typeof event.text === 'string'
          ) {
            streamedFormula = event.text;
            setFormula(event.text);
            if (!streamStarted) {
              streamStarted = true;
              setIsGenerating(false);
            }
          } else if (event.type === 'error' && event.error) {
            setFormula(event.error);
          }
        },
      });

      const finalFormula = streamedFormula || baseFormula;
      setFormula(finalFormula);

      if (!streamStarted) {
        setIsGenerating(false);
      }

      setGenerationStage('done');

      // Initial chat message
      const initialMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content:
          '¡Fórmula generada con éxito! ¿Tienes alguna pregunta o necesitas alguna aclaración?',
        timestamp: new Date(),
      };
      setChatMessages([initialMessage]);
    } catch (error) {
      console.error('[Step5Chat] Error generating formula:', error);
      const errorMessage = getErrorMessage(error);
      setFormula(errorMessage);
      setIsGenerating(false);
      setGenerationStage('done');
    }
  }, [currentAnalysis, desiredAnalysis, brand, productLine, clientName, technique, levelDifference, needsMultipleSessions]);

  // Generate formula only after data is loaded and validated
  useEffect(() => {
    if (!isLoaded) {
      console.log('[Step5] Esperando carga de datos del contexto...');
      return;
    }

    console.log('[Step5] DEBUG - FormulaData completo:', {
      hasCurrentImages: formulaData.currentColorImages?.length || 0,
      hasDesiredImages: formulaData.desiredColorImages?.length || 0,
      hasCurrentAnalysis: !!formulaData.currentColorAnalysis,
      hasDesiredAnalysis: !!formulaData.desiredColorAnalysis,
      hasBrand: !!formulaData.brand,
      brand: formulaData.brand,
      hasClient: !!selectedClient,
      clientName: selectedClient?.name,
    });

    if (!hasRequiredData) {
      console.error('[Step5] Datos incompletos:', {
        hasCurrentAnalysis: !!currentAnalysis,
        hasDesiredAnalysis: !!desiredAnalysis,
        hasBrand: !!brand,
      });
      setIsGenerating(false);
      setGenerationStage('done');
      setFormula('❌ **Error: Datos incompletos**\n\nFaltan datos necesarios para generar la fórmula. Por favor, completa todos los pasos anteriores:\n\n1. **Paso 1**: Análisis del color actual\n2. **Paso 2**: Análisis del color deseado\n3. **Paso 4**: Selección de marca\n\nLuego regresa a este paso para generar la fórmula.');
      return;
    }

    console.log('[Step5] Datos cargados correctamente, generando fórmula...');
    generateFormula();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoaded, hasRequiredData]);

  // =====================================================
  // CHAT FUNCTIONALITY
  // =====================================================

  const takePhoto = async () => {
    // Validate photo consent before allowing photo capture
    if (!formulaData.safetyChecklist?.photoConsentGiven) {
      Alert.alert(
        'Consentimiento requerido',
        'Necesitas el consentimiento del cliente para tomar fotos. Por favor, completa el checklist de seguridad en el paso 3.',
        [
          { text: 'Cancelar', style: 'cancel' },
          {
            text: 'Ir al paso 3',
            onPress: () => router.push(`/formula/step3?clientId=${clientId}`),
          },
        ]
      );
      return;
    }

    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permiso necesario', 'Se necesita permiso para acceder a la cámara');
      return;
    }

    hideAttachmentOptions();

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ['images'],
      allowsEditing: false,
      quality: 0.8,
    });

    if (!result.canceled && result.assets.length > 0) {
      const newImage = result.assets[0].uri;
      addImages([newImage]);
    }
  };

  const pickImage = async () => {
    // Validate photo consent before allowing photo selection
    if (!formulaData.safetyChecklist?.photoConsentGiven) {
      Alert.alert(
        'Consentimiento requerido',
        'Necesitas el consentimiento del cliente para usar fotos. Por favor, completa el checklist de seguridad en el paso 3.',
        [
          { text: 'Cancelar', style: 'cancel' },
          {
            text: 'Ir al paso 3',
            onPress: () => router.push(`/formula/step3?clientId=${clientId}`),
          },
        ]
      );
      return;
    }

    hideAttachmentOptions();

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: false,
      quality: 0.8,
      allowsMultipleSelection: true,
      selectionLimit: 6,
    });

    if (!result.canceled && result.assets.length > 0) {
      const newImages = result.assets.map((asset) => asset.uri);
      addImages(newImages);
    }
  };

  const updateLocalMemory = useCallback(async (
    messages: Message[],
    intentHint?: ConversationIntent
  ) => {
    try {
      const summary = await summarizeConversation({
        messages,
        previous: memorySnapshot,
        intentHint,
      });
      setMemorySnapshot(summary);
    } catch (error) {
      console.warn('[Step5Chat] Error generando memoria de chat', error);
    }
  }, [memorySnapshot]);

  const handleSendMessage = async () => {
    // Dismiss keyboard immediately when user sends message
    Keyboard.dismiss();

    hideAttachmentOptions();
    const trimmed = inputText.trim();
    const hasImages = selectedImages.length > 0;
    const previousMessages = [...chatMessages];

    // Allow sending if there's text OR images
    if ((!trimmed && !hasImages) || isSending) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: trimmed || '(Foto adjunta)',
      timestamp: new Date(),
      images: hasImages ? [...selectedImages] : undefined,
    };

    const rawImageUris = hasImages ? [...selectedImages] : [];

    // Don't set pendingAnchorIdRef here - we'll handle scroll in onContentSizeChange
    // after keyboard animation completes to avoid the visual "jerk"
    // pendingAnchorIdRef.current = userMessage.id;
    setChatMessages((prev) => [...prev, userMessage]);
    setInputText('');
    clearImages();
    setIsSending(true);

    try {
      if (!promptContext) {
        throw new Error('Context not available');
      }

      let intentResult: IntentDetectionResult | null = null;
      let detectedIntent: ConversationIntent = 'otro';

      const intentMessages = [...previousMessages, userMessage];

      try {
        intentResult = await detectConversationIntent({
          userMessage: trimmed || '(Foto adjunta)',
          recentMessages: intentMessages.map((msg) => ({
            ...msg,
            content: msg.content || '',
          })),
          memory: memorySnapshot,
        });
        detectedIntent = intentResult?.intent ?? 'otro';
        if (__DEV__) {
          console.log('[Step5Chat] Intent detection:', intentResult);
        }
      } catch (intentError) {
        console.warn('[Step5Chat] Intent detection failed', intentError);
      }

      const memorySection = formatMemoryForPrompt(memorySnapshot);
      const intentSection = describeIntent(detectedIntent);

      const conversationHistory = intentMessages
        .slice(-MAX_CONVERSATION_CONTEXT_MESSAGES)
        .map((msg) => ({
          role: msg.role as 'user' | 'assistant',
          content: msg.images && msg.images.length > 0
            ? `${msg.content} [imagen analizada previamente]`
            : msg.content,
        }));

      const baseSystemPrompt = getChatSystemPrompt(promptContext);
      const chatSystemPrompt = `${baseSystemPrompt}\n\n${memorySection}\n\n${intentSection}\n\nActúa como mentor de formulación. Usa la memoria para no repetir preguntas y ofrece próximos pasos concretos.`;

      const rawContext = buildFormulaContext(formulaData, formula || undefined, {
        memory: memorySnapshot,
        intent: detectedIntent,
      });
      const appContext = sanitizeContext(rawContext);

      // Process images: resize and then anonymize faces
      const resizedImageUris = hasImages ? await processMultipleImages(rawImageUris) : [];
      const currentImageUris = hasImages && resizedImageUris.length > 0
        ? await anonymizeImages(resizedImageUris)
        : undefined;

      if (!appContext) {
        console.warn('[Step5Chat] Failed to build valid context, proceeding without it');
      }

      const aiResponseContent = await generateTextSafe({
        messages: [
          { role: 'system', content: chatSystemPrompt },
          { role: 'user', content: trimmed || 'Analiza esta imagen en el contexto de la fórmula.' },
        ],
        imageUris: currentImageUris,
        imagesAlreadyProcessed: true,
        maxRetries: 2,
        retryDelay: 1500,
        useCase: hasImages ? 'vision_analysis' : 'chat',
        conversationHistory: conversationHistory.length > 0 ? conversationHistory : undefined,
        requestTimeout: CHAT_TIMEOUT_MS,
        appContext: appContext || undefined,
      });

      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: aiResponseContent,
        timestamp: new Date(),
      };

      pendingAnchorIdRef.current = aiResponse.id;
      setChatMessages((prev) => [...prev, aiResponse]);
      void updateLocalMemory([...intentMessages, aiResponse], detectedIntent);
    } catch (error) {
      console.error('[Step5Chat] Error sending message:', error);
      const errorMessage = getErrorMessage(error);

      const errorResponse: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: `Error: ${errorMessage}`,
        timestamp: new Date(),
      };

      pendingAnchorIdRef.current = errorResponse.id;
      setChatMessages((prev) => [...prev, errorResponse]);
    } finally {
      setIsSending(false);
    }
  };

  const handleQuickQuestion = (prompt: string) => {
    setInputText(prompt);
    setTimeout(() => {
      handleSendMessage();
    }, 100);
  };

  useEffect(() => {
    if (chatMessages.length === 0) {
      chatContainerOffsetRef.current = 0;
    }
  }, [chatMessages.length]);

  const renderListHeader = () => (
    <View>
      {chatMessages.length === 0 && (
        <ChatWelcomeHero variant="formula" style={styles.formulaWelcome} />
      )}

      {/* Hero Card */}
      <View style={styles.heroCard}>
        <View style={styles.heroHeader}>
          <Text style={styles.heroTitle}>
            {currentAnalysis && desiredAnalysis
              ? `Nivel ${currentAnalysis.roots.level} → ${desiredAnalysis.level}`
              : 'Transformación de color'}
          </Text>
          <Text style={styles.heroSubtitle}>
            {needsMultipleSessions ? `${Math.ceil(levelDifference / 3)} sesiones` : '1 sesión'} •{' '}
            {brand || 'Sin marca'}
          </Text>
        </View>

        {allPhotos.length > 0 && (
          <TouchableOpacity
            style={styles.heroPhotosButton}
            onPress={() => setShowPhotoPreview(true)}
          >
            <ImageIcon size={16} color={Colors.light.primary} strokeWidth={2} />
            <Text style={styles.heroPhotosText}>Ver {allPhotos.length} fotos</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Formula as Chat Message */}
      <View style={styles.formulaMessage}>
        <FormattedMessageContent content={formula} tone="assistant" />
      </View>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity style={styles.actionButton} onPress={handleSaveFormula}>
          <Save size={18} color={Colors.light.background} strokeWidth={2} />
          <Text style={styles.actionButtonText}>Guardar</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={() => setShowBrandPicker(true)}>
          <RefreshCw size={18} color={Colors.light.background} strokeWidth={2} />
          <Text style={styles.actionButtonText}>Cambiar marca</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleCopyFormula}>
          <Copy size={18} color={Colors.light.background} strokeWidth={2} />
          <Text style={styles.actionButtonText}>Copiar</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleShareFormula}>
          <Share2 size={18} color={Colors.light.background} strokeWidth={2} />
          <Text style={styles.actionButtonText}>Compartir</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleShowCosts}>
          <DollarSign size={18} color={Colors.light.background} strokeWidth={2} />
          <Text style={styles.actionButtonText}>Costos</Text>
        </TouchableOpacity>
      </View>

      {/* Quick Questions */}
      {quickQuestions.length > 0 && (
        <View style={styles.quickQuestionsContainer}>
          <Text style={styles.quickQuestionsTitle}>Preguntas rápidas:</Text>
          <View style={styles.quickQuestions}>
            {quickQuestions.slice(0, 4).map((q) => (
              <TouchableOpacity
                key={q.id}
                style={styles.quickQuestion}
                onPress={() => handleQuickQuestion(q.prompt)}
              >
                <Text style={styles.quickQuestionText}>{q.label}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {/* Chat Header */}
      {chatMessages.length > 0 && (
        <View
          style={styles.chatContainer}
          onLayout={({ nativeEvent }) => {
            chatContainerOffsetRef.current = nativeEvent.layout.y + nativeEvent.layout.height;
          }}
        >
          <Text style={styles.chatTitle}>Conversación:</Text>
        </View>
      )}
    </View>
  );

  const renderMessage = useCallback(
    ({ item, index }: { item: Message; index: number }) => (
      <MessageBubble
        message={item}
        variant="formula"
        onLayout={({ nativeEvent }) => {
          anchorMessagePosition(item.id, nativeEvent.layout.y, { index });
        }}
        onImageError={(uri, error) => {
          console.warn('[Step5Chat] Error cargando imagen:', uri, error);
        }}
      />
    ),
    [anchorMessagePosition]
  );

  // =====================================================
  // SAVE FORMULA
  // =====================================================

  const handleSaveFormula = async () => {
    // Input validation
    if (!selectedClient?.id) {
      Alert.alert('Error', 'Necesitas seleccionar un cliente para guardar la fórmula');
      return;
    }

    if (!formula || formula.trim().length === 0) {
      Alert.alert('Error', 'No hay fórmula para guardar');
      return;
    }

    if (!brand || brand.trim().length === 0) {
      Alert.alert('Error', 'La marca no está especificada');
      return;
    }

    if (!currentAnalysis || !desiredAnalysis) {
      Alert.alert('Error', 'Faltan datos de análisis de color');
      return;
    }

    // Safety checklist validation
    if (!formulaData.safetyChecklist?.photoConsentGiven) {
      Alert.alert(
        'Advertencia',
        'No se ha obtenido el consentimiento del cliente para fotos. La fórmula se guardará sin imágenes.',
        [{ text: 'Entendido' }]
      );
    }

    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) {
        Alert.alert('Error', 'Usuario no autenticado');
        return;
      }

      const sessionNumber = (await getLatestSessionNumber(selectedClient.id)) + 1;
      const products = extractProductsFromText(formula);

      // Sanitize inputs before saving
      const sanitizedFormula = formula.trim().substring(0, 50000); // Max 50k chars
      const sanitizedBrand = brand.trim().substring(0, 200);
      const sanitizedProductLine = productLine?.trim().substring(0, 200);
      const sanitizedTechnique = technique?.trim().substring(0, 200);
      const sanitizedTargetTone = desiredAnalysis?.tone?.trim().substring(0, 100);
      const sanitizedTargetReflection = desiredAnalysis?.reflection?.trim().substring(0, 100);

      await saveFormula({
        clientId: selectedClient.id,
        userId: userData.user.id,
        sessionNumber,
        serviceType: 'color',
        currentLevel: currentAnalysis?.roots.level,
        targetLevel: desiredAnalysis?.level,
        targetTone: sanitizedTargetTone,
        targetReflection: sanitizedTargetReflection,
        brand: sanitizedBrand,
        productLine: sanitizedProductLine,
        technique: sanitizedTechnique,
        formulaText: sanitizedFormula,
        productsUsed: products,
        totalCost: estimateCost(products),
        currentColorAnalysis: currentAnalysis,
        desiredColorAnalysis: desiredAnalysis,
        safetyChecklist: formulaData.safetyChecklist,
      });

      Alert.alert('Éxito', `Fórmula guardada en historial del cliente (Sesión ${sessionNumber})`);
    } catch (error) {
      console.error('[Step5Chat] Error saving formula:', error);
      Alert.alert('Error', 'No se pudo guardar la fórmula');
    }
  };

  // =====================================================
  // CHANGE BRAND & REGENERATE
  // =====================================================

  const handleChangeBrand = async (newBrand: string) => {
    setShowBrandPicker(false);
    setIsGenerating(true);
    setGenerationStage('drafting');

    try {
      if (!currentAnalysis || !desiredAnalysis) {
        throw new Error('Datos no disponibles');
      }

      const newContext: FormulaPromptContext = {
        brand: newBrand,
        productLine: undefined,
        currentAnalysis,
        desiredAnalysis,
        clientName,
        technique,
        mixingRatioInfo: undefined,
        levelDifference,
        needsMultipleSessions,
      };

      const systemPrompt = getFormulaSystemPrompt(newContext);
      const userPrompt = getFormulaUserPrompt(newContext);

      const newFormula = await generateTextSafe({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt },
        ],
        maxRetries: 2,
        useCase: 'formula_generation',
        requestTimeout: FORMULA_GENERATION_TIMEOUT_MS,
      });

      setFormula(newFormula);
      Alert.alert('Éxito', `Fórmula regenerada con ${newBrand}`);
    } catch (error) {
      console.error('[Step5Chat] Error changing brand:', error);
      Alert.alert('Error', 'No se pudo regenerar la fórmula');
    } finally {
      setIsGenerating(false);
      setGenerationStage('done');
    }
  };

  // =====================================================
  // SHARE & COPY
  // =====================================================

  const handleCopyFormula = () => {
    Clipboard.setString(formula);
    Alert.alert('Copiado', 'Fórmula copiada al portapapeles');
  };

  const handleShareFormula = async () => {
    try {
      await Share.share({
        message: `Fórmula de coloración - ${clientName || 'Cliente'}\n\n${formula}`,
        title: 'Fórmula de coloración',
      });
    } catch (error) {
      console.error('[Step5Chat] Error sharing:', error);
    }
  };

  // =====================================================
  // COST CALCULATOR
  // =====================================================

  const handleShowCosts = () => {
    const products = extractProductsFromText(formula);
    const cost = estimateCost(products);

    Alert.alert(
      'Costos estimados',
      `Productos: $${cost.toFixed(2)}\n\nPrecio sugerido al cliente:\n• Básico (x2.5): $${(
        cost * 2.5
      ).toFixed(2)}\n• Estándar (x3.5): $${(cost * 3.5).toFixed(2)}\n• Premium (x4.5): $${(
        cost * 4.5
      ).toFixed(2)}`,
      [{ text: 'Cerrar' }]
    );
  };

  // =====================================================
  // FINISH & UPLOAD
  // =====================================================

  const saveFormulaToDatabase = async () => {
    if (!selectedClient?.id || !formula || !currentAnalysis || !desiredAnalysis || !brand) {
      console.warn('[Step5] No se puede guardar: faltan datos');
      return null;
    }

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('[Step5] Usuario no autenticado');
        return null;
      }

      // Upload photos first
      const photosToUpload = allPhotos.filter((uri) => uri && uri.startsWith('file://'));
      let uploadedPhotoUrls: string[] = [];

      if (photosToUpload.length > 0) {
        console.log(`[Step5] Uploading ${photosToUpload.length} photos...`);
        const uploadResults = await Promise.all(
          photosToUpload.map(async (uri) => {
            try {
              const result = await uploadFormulaPhoto(uri, selectedClient.id);
              return result;
            } catch (err) {
              console.error('[Step5] Error uploading photo:', err);
              return null;
            }
          })
        );
        uploadedPhotoUrls = uploadResults.filter((url): url is string => url !== null);
        console.log('[Step5] Photos uploaded successfully:', uploadedPhotoUrls.length);
      }

      // Save formula to database
      const formulaData = {
        user_id: user.id,
        client_id: selectedClient.id,
        session_number: 1, // TODO: Calculate next session number
        service_type: 'color', // Default to color service
        current_level: currentAnalysis.roots.level,
        target_level: desiredAnalysis.level,
        target_tone: desiredAnalysis.tone || null,
        target_reflection: desiredAnalysis.reflection || null,
        brand: brand,
        product_line: productLine || null,
        technique: technique || null,
        formula_text: formula,
        current_color_analysis: currentAnalysis,
        desired_color_analysis: desiredAnalysis,
      };

      console.log('[Step5] Guardando fórmula en base de datos...');
      const { data, error } = await supabase
        .from('formulas')
        .insert(formulaData)
        .select()
        .single();

      if (error) {
        console.error('[Step5] Error guardando fórmula:', error);
        throw error;
      }

      console.log('[Step5] ✅ Fórmula guardada exitosamente:', data.id);
      return data.id;
    } catch (error) {
      console.error('[Step5] Error en saveFormulaToDatabase:', error);
      throw error;
    }
  };

  const handleFinish = async () => {
    if (!selectedClient?.id) {
      Alert.alert(
        'Cliente requerido',
        'Necesitas seleccionar un cliente para guardar la fórmula.',
        [
          {
            text: 'Seleccionar cliente',
            onPress: () => router.push('/formula/step0'),
          },
          {
            text: 'Cancelar',
            style: 'cancel',
          },
        ]
      );
      return;
    }

    setIsUploadingPhotos(true);

    try {
      // Save formula to database (includes photo upload)
      const formulaId = await saveFormulaToDatabase();

      if (formulaId) {
        console.log('[Step5] ✅ Fórmula guardada con ID:', formulaId);
        Alert.alert(
          '✅ Fórmula guardada',
          'La fórmula ha sido guardada exitosamente.',
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate FIRST, then reset (prevents data clearing before navigation)
                router.replace('/(app)/(tabs)/chat');
                // Reset after next render cycle (more reliable than setTimeout)
                requestAnimationFrame(() => resetFormula());
              },
            },
          ]
        );
      } else {
        throw new Error('No se pudo guardar la fórmula');
      }
    } catch (error) {
      console.error('[Step5] Error al finalizar:', error);
      Alert.alert(
        'Error al guardar',
        'Hubo un problema al guardar la fórmula. ¿Deseas finalizar de todas formas?',
        [
          { text: 'Cancelar', style: 'cancel' },
          {
            text: 'Finalizar sin guardar',
            style: 'destructive',
            onPress: () => {
              // Navigate FIRST, then reset (prevents data clearing before navigation)
              router.replace('/(app)/(tabs)/chat');
              // Reset after next render cycle (more reliable than setTimeout)
              requestAnimationFrame(() => resetFormula());
            },
          },
        ]
      );
    } finally {
      setIsUploadingPhotos(false);
    }
  };

  // =====================================================
  // RENDER: LOADING
  // =====================================================

  if (isGenerating && !formula) {
    return (
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.light.primary} />
          <Text style={styles.loadingTitle}>Preparando tu fórmula profesional</Text>
          <Text style={styles.loadingSubtitle}>
            {generationStage === 'analysis' && 'Analizando diagnóstico y objetivo...'}
            {generationStage === 'validation' && 'Validando coherencia de datos...'}
            {generationStage === 'references' && 'Verificando proporciones del fabricante...'}
            {generationStage === 'drafting' && 'Generando plan paso a paso...'}
          </Text>
        </View>
      </View>
    );
  }

  // =====================================================
  // RENDER: MAIN UI
  // =====================================================

  // Show loading screen while context data is loading
  if (!isLoaded) {
    return (
      <View style={[styles.container, { paddingTop: insets.top, justifyContent: 'center', alignItems: 'center' }]}>
        <ActivityIndicator size="large" color={Colors.light.primary} />
        <Text style={[styles.heroSubtitle, { marginTop: 16 }]}>
          Cargando datos de la formulación...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <ProgressIndicator currentStep={6} totalSteps={6} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 16 : 0}
      >
        <FlatList
          ref={flatListRef}
          testID="formula-step5-scroll"
          style={styles.scrollView}
          contentContainerStyle={[styles.scrollContent, { paddingBottom: scrollBottomPadding }]}
          data={chatMessages}
          renderItem={renderMessage}
          keyExtractor={item => item.id}
          ListHeaderComponent={renderListHeader}
          showsVerticalScrollIndicator={false}
          keyboardDismissMode="interactive"
          keyboardShouldPersistTaps="handled"
          onContentSizeChange={() => {
            // Execute scroll AFTER keyboard animation completes AND content finishes rendering
            // This prevents the visual "jerk" from double scroll (immediate + keyboard adjustment)
            if (shouldAutoScrollRef.current) {
              shouldAutoScrollRef.current = false;
              requestAnimationFrame(() => {
                flatListRef.current?.scrollToEnd({ animated: true });
              });
            }
          }}
        />

        {/* Chat Input */}
        <View style={[styles.inputContainer, { paddingBottom: inputBottomPadding }]}> 
          {/* Image Preview */}
          {selectedImages.length > 0 && (
            <View style={styles.imagesPreviewContainer}>
              {selectedImages.map((imgUri, index) => (
                <View key={index} style={styles.imagePreviewWrapper}>
                  <Image source={{ uri: imgUri }} style={styles.imagePreview} />
                  <TouchableOpacity
                    style={styles.removeImageButton}
                    onPress={() => removeImageAt(index)}
                  >
                    <Text style={styles.removeImageText}>✕</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}

          {showAttachmentOptions && (
            <View style={styles.attachmentsCard}>
              <View style={styles.attachmentsHeader}>
                <Text style={styles.attachmentsTitle}>Añadir al chat</Text>
                <TouchableOpacity
                  style={styles.attachmentsClose}
                  onPress={hideAttachmentOptions}
                >
                  <X color={Colors.light.textSecondary} size={18} strokeWidth={2} />
                </TouchableOpacity>
              </View>
              <View style={styles.attachmentRow}>
                <TouchableOpacity style={styles.attachmentOption} onPress={takePhoto}>
                  <View style={styles.attachmentIconBadge}>
                    <Camera color={Colors.light.primary} size={18} strokeWidth={2} />
                  </View>
                  <Text style={styles.attachmentLabel}>Cámara</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.attachmentOption} onPress={pickImage}>
                  <View style={styles.attachmentIconBadge}>
                    <ImageIcon color={Colors.light.primary} size={18} strokeWidth={2} />
                  </View>
                  <Text style={styles.attachmentLabel}>Fotos</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}

          {/* Input Row */}
          <ChatComposer
            variant="formula"
            value={inputText}
            placeholder="Pregunta sobre la fórmula..."
            onChangeText={setInputText}
            onSend={handleSendMessage}
            disabled={(!inputText.trim() && selectedImages.length === 0) || isSending}
            isSending={isSending}
            showSendingIndicator
            onPressAttachment={toggleAttachmentOptions}
            onFocusInput={hideAttachmentOptions}
            sendButtonTestID="formula-step5-send"
          />
        </View>

        {/* Finish Button */}
        <View style={[styles.finishContainer, { paddingBottom: insets.bottom || 16 }]}>
          <TouchableOpacity
            style={styles.finishButton}
            onPress={handleFinish}
            disabled={isUploadingPhotos}
          >
            {isUploadingPhotos ? (
              <ActivityIndicator size="small" color={Colors.light.background} />
            ) : (
              <>
                <Check size={20} color={Colors.light.background} strokeWidth={2.5} />
                <Text style={styles.finishButtonText}>Finalizar</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>

      {/* Photo Preview Modal */}
      <Modal visible={showPhotoPreview} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Fotos de referencia</Text>
              <TouchableOpacity onPress={() => setShowPhotoPreview(false)}>
                <X size={24} color={Colors.light.text} strokeWidth={2} />
              </TouchableOpacity>
            </View>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {allPhotos.map((uri, idx) => (
                <Image key={idx} source={{ uri }} style={styles.photoPreview} />
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Brand Picker Modal */}
      <Modal visible={showBrandPicker} transparent animationType="slide">
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>¿No tienes {brand}?</Text>
              <TouchableOpacity onPress={() => setShowBrandPicker(false)}>
                <X size={24} color={Colors.light.text} strokeWidth={2} />
              </TouchableOpacity>
            </View>
            <Text style={styles.modalSubtitle}>
              Regeneraré esta MISMA fórmula con otra marca:
            </Text>
            <ScrollView>
              {alternativeBrands
                .filter((b) => b !== brand)
                .map((alternativeBrand) => (
                  <TouchableOpacity
                    key={alternativeBrand}
                    style={styles.brandOption}
                    onPress={() => handleChangeBrand(alternativeBrand)}
                  >
                    <Text style={styles.brandOptionText}>{alternativeBrand}</Text>
                  </TouchableOpacity>
                ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
}

// =====================================================
// STYLES
// =====================================================

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  keyboardView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: space.lg,  // 16px
    paddingBottom: 120,
  },
  formulaWelcome: {
    marginBottom: space.xl,  // 20px
  },

  // Loading
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: space.xxxl,  // 32px
  },
  loadingTitle: {
    ...textStyles.h4,  // 18px, 600 weight, -0.2 spacing
    color: Colors.light.text,
    marginTop: space.lg,  // 16px
    textAlign: 'center',
  },
  loadingSubtitle: {
    ...textStyles.secondary,  // 13px, 400 weight
    color: Colors.light.textSecondary,
    marginTop: space.xs,  // 8px
    textAlign: 'center',
  },

  // Hero Card
  heroCard: {
    backgroundColor: Colors.light.card,
    borderRadius: borderRadius.lg,  // 16px
    padding: space.lg,  // 16px
    marginBottom: space.lg,  // 16px
    shadowColor: '#000',
    ...shadow.md,  // Claude-style shadow
  },
  heroHeader: {
    marginBottom: space.md,  // 12px
  },
  heroTitle: {
    ...textStyles.h3,  // 20px, 700 weight, -0.3 spacing
    color: Colors.light.text,
    marginBottom: space.xs,  // 4px
  },
  heroSubtitle: {
    ...textStyles.secondary,  // 13px, 400 weight
    color: Colors.light.textSecondary,
  },
  heroPhotosButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: space.xs + 2,  // 6px
    paddingVertical: space.xs + 4,  // 8px
    paddingHorizontal: space.md,  // 12px
    backgroundColor: `${Colors.light.primary}15`,
    borderRadius: borderRadius.sm,  // 8px
    alignSelf: 'flex-start',
  },
  heroPhotosText: {
    ...textStyles.secondary,  // 13px base
    fontWeight: '600',
    color: Colors.light.primary,
  },

  // Formula Message
  formulaMessage: {
    backgroundColor: Colors.light.card,
    borderRadius: borderRadius.lg,  // 16px
    padding: space.xl,  // 20px
    marginBottom: space.lg,  // 16px
    shadowColor: '#000',
    ...shadow.md,  // Claude-style shadow
  },

  // Action Buttons
  actionButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: space.sm,  // 8px
    marginBottom: space.lg,  // 16px
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: space.xs + 2,  // 6px
    paddingVertical: 10,
    paddingHorizontal: 14,
    backgroundColor: Colors.light.primary,
    borderRadius: borderRadius.md,  // 12px
    shadowColor: Colors.light.primary,
    ...shadow.primary,  // Primary accent shadow
  },
  actionButtonText: {
    ...textStyles.secondary,  // 13px base
    fontWeight: '600',
    color: Colors.light.background,
  },

  // Quick Questions
  quickQuestionsContainer: {
    marginBottom: space.lg,  // 16px
  },
  quickQuestionsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: space.sm,  // 8px
  },
  quickQuestions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: space.sm,  // 8px
  },
  quickQuestion: {
    paddingVertical: space.sm,  // 8px
    paddingHorizontal: space.md,  // 12px
    backgroundColor: Colors.light.card,
    borderRadius: borderRadius.xl,  // 20px
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  quickQuestionText: {
    ...textStyles.caption,  // 11px
    fontSize: 12,
    color: Colors.light.textSecondary,
  },

  // Chat
  chatContainer: {
    marginBottom: space.lg,  // 16px
  },
  chatTitle: {
    ...textStyles.bodyLarge,  // 17px base
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: space.md,  // 12px
  },
  // Contenedor para cada mensaje completo (imágenes + bubble)

  // Input
  inputContainer: {
    paddingHorizontal: space.lg,  // 16px
    paddingTop: space.sm,  // 8px (optimizado para máximo espacio útil)
    backgroundColor: 'transparent',
  },
  imagesPreviewContainer: {
    flexDirection: 'row',
    gap: space.sm,  // 8px
    paddingBottom: space.sm,  // 8px
    flexWrap: 'wrap',
  },
  imagePreviewWrapper: {
    position: 'relative',
  },
  imagePreview: {
    width: 80,
    height: 80,
    borderRadius: borderRadius.lg,  // 16px
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.light.error,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    ...shadow.md,
  },
  removeImageText: {
    color: Colors.light.background,
    fontSize: 15,
    fontWeight: '700',
    lineHeight: 20,
  },
  attachmentsCard: {
    alignSelf: 'stretch',
    marginBottom: space.lg,  // 16px
    padding: space.lg,  // 16px
    borderRadius: borderRadius.huge,  // 28px (Claude-style)
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    ...shadow.lg,  // Claude-style gentle shadow
    gap: space.lg,  // 16px
  },
  attachmentsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  attachmentsTitle: {
    ...textStyles.button,  // 15px base
    color: Colors.light.text,
  },
  attachmentsClose: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
  },
  attachmentRow: {
    flexDirection: 'row',
    gap: space.md,  // 12px
  },
  attachmentOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: space.md,  // 12px
    paddingVertical: 10,
    paddingHorizontal: 14,
    borderRadius: borderRadius.xl,  // 20px
    backgroundColor: Colors.light.backgroundSecondary,
  },
  attachmentIconBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.background,
  },
  attachmentLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.textSecondary,
  },

  // Finish
  finishContainer: {
    paddingHorizontal: space.lg,  // 16px
    paddingTop: space.md,  // 12px
    backgroundColor: Colors.light.background,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  finishButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: space.sm,  // 8px
    backgroundColor: Colors.light.accent,
    paddingVertical: 14,
    borderRadius: borderRadius.md,  // 12px
    shadowColor: Colors.light.accent,
    ...shadow.primary,  // Primary accent shadow
  },
  finishButtonText: {
    ...textStyles.bodyLarge,  // 17px base
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.background,
  },

  // Modals
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: space.xl,  // 20px
  },
  modalContent: {
    backgroundColor: Colors.light.background,
    borderRadius: borderRadius.xl,  // 20px
    padding: space.xl,  // 20px
    width: '100%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: space.lg,  // 16px
  },
  modalTitle: {
    ...textStyles.h3,  // 20px, 700 weight, -0.3 spacing
    color: Colors.light.text,
  },
  modalSubtitle: {
    ...textStyles.secondary,  // 13px
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: space.lg,  // 16px
  },
  photoPreview: {
    width: 250,
    height: 350,
    borderRadius: borderRadius.md,  // 12px
    marginRight: space.md,  // 12px
  },
  brandOption: {
    paddingVertical: space.lg,  // 16px
    paddingHorizontal: space.lg,  // 16px
    backgroundColor: Colors.light.card,
    borderRadius: borderRadius.md,  // 12px
    marginBottom: space.sm,  // 8px
  },
  brandOptionText: {
    ...textStyles.bodyLarge,  // 17px base
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
});
