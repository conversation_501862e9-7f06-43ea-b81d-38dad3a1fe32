import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  TextInput,
  Modal,
  Pressable,
  Alert,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import { ChevronRight, X, ChevronDown, Check, ChevronUp, ImageIcon, Camera } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useFormula } from '@/contexts/FormulaContext';
import { useClients } from '@/contexts/ClientContext';
import { generateTextSafe } from '@/lib/ai-client';
import { sanitizeTextForDisplay, sanitizeHexColor, validateClientAccess } from '@/lib/sanitize';
import type { DesiredColorAnalysis, DesiredColorZone } from '@/types';
import PhotoGuidance from '@/components/PhotoGuidance';
import { showVisionSafetyError, isVisionSafetyError } from '@/lib/vision-safety-utils';
import ProgressIndicator from '@/components/ProgressIndicator';
import { processImageForHairAnalysis, processMultipleImages } from '@/lib/imageProcessor';
import { useAnonymizer } from '@/contexts/AnonymizerContext';

type Section = 'general' | 'roots' | 'mids' | 'ends';

type ToneOption = {
  value: string;
  label: string;
  color: string;
};

type TechniqueOption = {
  value: string;
  label: string;
  description: string;
};

export default function Step2Screen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const { clientId } = useLocalSearchParams<{ clientId: string }>();
  const { clients } = useClients();
  const { formulaData, selectedClient, selectClient, updateDesiredColor } = useFormula();
  const { anonymizeImages } = useAnonymizer();

  // Restore client from route params if context is empty (with IDOR protection)
  React.useEffect(() => {
    if (clientId && !selectedClient) {
      // SECURITY: Validate client access before restoring (IDOR protection)
      const validatedClient = validateClientAccess(clientId, clients);
      if (validatedClient) {
        selectClient(validatedClient);
      } else {
        console.error('[Step2][SECURITY] Unauthorized client access attempt:', clientId);
        Alert.alert(
          'Error',
          'No tienes acceso a este cliente.',
          [{ text: 'OK', onPress: () => router.back() }]
        );
      }
    }
  }, [clientId, selectedClient, clients, selectClient, router]);

  const [images, setImages] = useState<string[]>(formulaData.desiredColorImages || []);
  const [analysis, setAnalysis] = useState<DesiredColorAnalysis | undefined>(formulaData.desiredColorAnalysis);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Set<Section>>(new Set(['general']));

  const [showToneModal, setShowToneModal] = useState(false);
  const [showReflectionModal, setShowReflectionModal] = useState(false);
  const [showTechniqueModal, setShowTechniqueModal] = useState(false);
  const [modalTarget, setModalTarget] = useState<{
    zone?: 'roots' | 'mids' | 'ends' | 'general';
    field: string;
  } | null>(null);

  // Fix #6: AbortController to cancel AI analysis on unmount (memory leak prevention)
  const abortControllerRef = useRef<AbortController | null>(null);

  // Fix #6: Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        console.log('[Step2] Aborting ongoing AI analysis on unmount');
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
    };
  }, []);

  const toggleSection = (section: Section) => {
    setExpandedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(section)) {
        newSet.delete(section);
      } else {
        newSet.add(section);
      }
      return newSet;
    });
  };

  const toneOptions: ToneOption[] = [
    { value: 'Rubio platino', label: 'Rubio platino', color: '#F5F5DC' },
    { value: 'Rubio claro', label: 'Rubio claro', color: '#F0E68C' },
    { value: 'Rubio medio', label: 'Rubio medio', color: '#DAA520' },
    { value: 'Rubio oscuro', label: 'Rubio oscuro', color: '#B8860B' },
    { value: 'Castaño claro', label: 'Castaño claro', color: '#CD853F' },
    { value: 'Castaño medio', label: 'Castaño medio', color: '#8B4513' },
    { value: 'Castaño oscuro', label: 'Castaño oscuro', color: '#5C4033' },
    { value: 'Negro', label: 'Negro', color: '#1A1A1A' },
    { value: 'Pelirrojo', label: 'Pelirrojo', color: '#C1440E' },
    { value: 'Cobrizo', label: 'Cobrizo', color: '#B87333' },
  ];

  const reflectionOptions: ToneOption[] = [
    { value: 'Ceniza', label: 'Ceniza', color: '#C0C0C0' },
    { value: 'Dorado', label: 'Dorado', color: '#FFD700' },
    { value: 'Cobrizo', label: 'Cobrizo', color: '#B87333' },
    { value: 'Rojizo', label: 'Rojizo', color: '#DC143C' },
    { value: 'Caoba', label: 'Caoba', color: '#C04000' },
    { value: 'Violeta', label: 'Violeta', color: '#8B00FF' },
    { value: 'Perla', label: 'Perla', color: '#E8E8E8' },
    { value: 'Beige', label: 'Beige', color: '#D2B48C' },
    { value: 'Natural', label: 'Natural', color: '#8B7355' },
    { value: 'Neutro', label: 'Neutro', color: '#A0A0A0' },
  ];

  const techniqueOptions: TechniqueOption[] = [
    { value: 'Coloración global', label: 'Coloración global', description: 'Color uniforme en todo el cabello' },
    { value: 'Mechas', label: 'Mechas', description: 'Efectos de contraste con mechas' },
    { value: 'Balayage', label: 'Balayage', description: 'Efecto degradado natural' },
    { value: 'Ombré', label: 'Ombré', description: 'Transición de color desde raíces a puntas' },
    { value: 'Babylights', label: 'Babylights', description: 'Mechas muy finas y naturales' },
    { value: 'Highlights', label: 'Highlights', description: 'Mechas más claras' },
    { value: 'Lowlights', label: 'Lowlights', description: 'Mechas más oscuras' },
    { value: 'Solo raíces', label: 'Solo raíces', description: 'Aplicación únicamente en raíces' },
  ];

  const pickImages = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsMultipleSelection: true,
      quality: 0.8,
    });

    if (!result.canceled && result.assets.length > 0) {
      setIsUploading(true);
      try {
        // Procesar cada imagen con el pipeline centralizado (896px + compresión)
        const rawUris = result.assets.map((asset) => asset.uri);
        const resizedUris = await processMultipleImages(rawUris);

        // Anonimizar caras para evitar VisionSafetyError
        const processedUris = await anonymizeImages(resizedUris);

        // Guardar URIs locales procesadas (NO subir a Storage todavía)
        const newImages = [...images, ...processedUris].slice(0, 6);
        setImages(newImages);
      } catch (error) {
        console.error('Error al procesar imágenes:', error);
        alert('Error al procesar las imágenes. Por favor, intenta de nuevo.');
      } finally {
        setIsUploading(false);
      }
    }
  };

  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permiso necesario', 'Se necesita permiso para acceder a la cámara');
      return;
    }

    setIsUploading(true);
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images'],
        allowsEditing: false,
        quality: 0.8,
      });

      if (!result.canceled && result.assets.length > 0) {
        const resizedUri = await processImageForHairAnalysis(result.assets[0].uri);

        // Anonimizar caras para evitar VisionSafetyError
        const [processedUri] = await anonymizeImages([resizedUri]);

        const newImages = [...images, processedUri].slice(0, 6);
        setImages(newImages);
      }
    } catch (error) {
      console.error('Error al procesar foto:', error);
      Alert.alert('Error', 'Error al procesar la foto. Por favor, intenta de nuevo.');
    } finally {
      setIsUploading(false);
    }
  };

  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    setImages(newImages);

    // Invalidar verificación siempre que se eliminen fotos (análisis puede estar desactualizado)
    if (analysis && isVerified) {
      setIsVerified(false);
    }

    // Si se eliminan todas las imágenes, limpiar el análisis completamente
    if (newImages.length === 0) {
      setAnalysis(undefined);
      setIsVerified(false);
    }
  };

  const analyzeImages = async () => {
    if (images.length === 0) return;

    setIsAnalyzing(true);

    // Fix #6: Create AbortController for this AI analysis (memory leak prevention)
    abortControllerRef.current = new AbortController();

    try {
      const currentColorInfo = formulaData.currentColorAnalysis
        ? `\n\nINFORMACIÓN DEL COLOR ACTUAL (para referencia):
- Nivel actual raíces: ${formulaData.currentColorAnalysis.roots.level}/10
- Tono actual predominante: ${formulaData.currentColorAnalysis.generalCharacteristics.predominantTone}
- Reflejo actual predominante: ${formulaData.currentColorAnalysis.generalCharacteristics.predominantReflection}
- Porcentaje de canas: ${formulaData.currentColorAnalysis.grayAnalysis.percentage}%
- Estado raíces: ${formulaData.currentColorAnalysis.roots.state}
- Estado medios: ${formulaData.currentColorAnalysis.mids.state}
- Estado puntas: ${formulaData.currentColorAnalysis.ends.state}`
        : '';

      // IMPORTANTE: Usar el mismo systemPrompt que step1 (que SÍ funciona)
      // El prompt en español es clave - el prompt en inglés del edge function falla
      const systemPrompt = `Eres un experto analista de coloración capilar profesional especializado en análisis técnico de cabello.

IMPORTANTE: Tu trabajo es analizar EXCLUSIVAMENTE el cabello visible en las imágenes. NO analices ni comentes sobre rostros, personas, identidades, características faciales, edad, género, raza o cualquier característica personal. Si aparece una persona en la imagen, ignórala completamente y enfócate SOLO en el cabello.

Tu análisis debe enfocarse únicamente en el COLOR DESEADO del cabello:
- Color objetivo (nivel, tono, reflejo)
- Técnica de aplicación sugerida
- Cobertura de canas si aplica
- Resultado esperado (natural/vibrante/fantasía)${currentColorInfo}

Debes devolver un objeto JSON con esta estructura EXACTA:
{
  "level": número del 1 al 10 (1=negro, 10=rubio muy claro) - nivel deseado general,
  "tone": "nombre del tono base deseado" (ej: "Rubio medio", "Castaño claro", etc.),
  "toneColor": "código hex del color",
  "reflection": "nombre del reflejo/matiz deseado" (ej: "Ceniza", "Dorado", "Cobrizo", etc.),
  "reflectionColor": "código hex del reflejo",
  "reflectionIntensity": "sutil" | "medio" | "intenso",
  "grayCoverage": número 0-100 (porcentaje de cobertura de canas necesaria),
  "resultType": "natural" | "vibrante" | "fantasía",
  "technique": "técnica sugerida" (ej: "Coloración global", "Balayage", "Mechas", etc.),
  "colorDepth": "sutil" | "medio" | "profundo",
  "roots": {
    "level": número 1-10 (nivel deseado en raíces),
    "tone": "tono deseado en raíces",
    "toneColor": "hex",
    "reflection": "reflejo deseado en raíces",
    "reflectionColor": "hex"
  },
  "mids": {
    "level": número 1-10 (nivel deseado en medios),
    "tone": "tono deseado en medios",
    "toneColor": "hex",
    "reflection": "reflejo deseado en medios",
    "reflectionColor": "hex"
  },
  "ends": {
    "level": número 1-10 (nivel deseado en puntas),
    "tone": "tono deseado en puntas",
    "toneColor": "hex",
    "reflection": "reflejo deseado en puntas",
    "reflectionColor": "hex"
  },
  "notes": "notas adicionales sobre el resultado deseado"
}

Analiza cada zona (raíces, medios, puntas) por separado del color deseado. Si la técnica es uniforme, los valores pueden ser similares en todas las zonas.`;

      const response = await generateTextSafe({
        messages: [
          { role: 'system', content: systemPrompt },
          {
            role: 'user',
            content: 'Analiza SOLO el cabello visible en estas fotos mostrando el color deseado. Ignora cualquier rostro o persona que aparezca. Proporciona el análisis técnico completo del color deseado en formato JSON puro (sin markdown, sin explicaciones adicionales).',
          },
        ],
        maxRetries: 2,
        retryDelay: 1500,
        useCase: 'vision_analysis',
        imageUris: images,
        imagesAlreadyProcessed: true,
        requestTimeout: 120000, // Vision puede tardar con referencias múltiples; ampliamos margen
        signal: abortControllerRef.current.signal, // Fix #6: Abort on unmount
      });

      // Robust JSON parsing (handles markdown, extra text, etc.)
      let parsedAnalysis: DesiredColorAnalysis | null = null;

      // Try 1: Extract JSON from markdown code block
      const markdownMatch = response.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
      if (markdownMatch) {
        try {
          parsedAnalysis = JSON.parse(markdownMatch[1]);
        } catch (e) {
          console.warn('[Step2] Failed to parse markdown JSON:', e);
        }
      }

      // Try 2: Extract raw JSON object
      if (!parsedAnalysis) {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          try {
            parsedAnalysis = JSON.parse(jsonMatch[0]);
          } catch (e) {
            console.warn('[Step2] Failed to parse raw JSON:', e);
          }
        }
      }

      if (parsedAnalysis) {
        // SECURITY: Sanitize all text fields from AI response to prevent XSS
        const sanitizedAnalysis: DesiredColorAnalysis = {
          ...parsedAnalysis,
          tone: sanitizeTextForDisplay(parsedAnalysis.tone),
          toneColor: sanitizeHexColor(parsedAnalysis.toneColor),
          reflection: sanitizeTextForDisplay(parsedAnalysis.reflection),
          reflectionColor: sanitizeHexColor(parsedAnalysis.reflectionColor),
          reflectionIntensity: parsedAnalysis.reflectionIntensity, // enum - safe
          resultType: parsedAnalysis.resultType, // enum - safe
          technique: sanitizeTextForDisplay(parsedAnalysis.technique),
          colorDepth: parsedAnalysis.colorDepth, // enum - safe
          notes: sanitizeTextForDisplay(parsedAnalysis.notes),
          roots: {
            ...parsedAnalysis.roots,
            tone: sanitizeTextForDisplay(parsedAnalysis.roots.tone),
            toneColor: sanitizeHexColor(parsedAnalysis.roots.toneColor),
            reflection: sanitizeTextForDisplay(parsedAnalysis.roots.reflection),
            reflectionColor: sanitizeHexColor(parsedAnalysis.roots.reflectionColor),
          },
          mids: {
            ...parsedAnalysis.mids,
            tone: sanitizeTextForDisplay(parsedAnalysis.mids.tone),
            toneColor: sanitizeHexColor(parsedAnalysis.mids.toneColor),
            reflection: sanitizeTextForDisplay(parsedAnalysis.mids.reflection),
            reflectionColor: sanitizeHexColor(parsedAnalysis.mids.reflectionColor),
          },
          ends: {
            ...parsedAnalysis.ends,
            tone: sanitizeTextForDisplay(parsedAnalysis.ends.tone),
            toneColor: sanitizeHexColor(parsedAnalysis.ends.toneColor),
            reflection: sanitizeTextForDisplay(parsedAnalysis.ends.reflection),
            reflectionColor: sanitizeHexColor(parsedAnalysis.ends.reflectionColor),
          },
        };

        setAnalysis(sanitizedAnalysis);
        setExpandedSections(new Set(['general', 'roots']));
      } else {
        console.error('[Step2] Could not extract JSON. Full response:', response);
        throw new Error('No se pudo parsear el análisis. La respuesta no contiene JSON válido.');
      }
    } catch (error: any) {
      console.error('[Step2] Error analyzing desired color:', error);

      // Vision safety rejection handling
      if (isVisionSafetyError(error)) {
        showVisionSafetyError(
          () => setImages([]),
          () => analyzeImages()
        );
        return;
      }

    } finally {
      setIsAnalyzing(false);
      // Fix #6: Cleanup AbortController after analysis completes
      abortControllerRef.current = null;
    }
  };

  const handleSave = () => {
    if (!analysis || images.length === 0) {
      alert('Por favor, sube y analiza al menos una foto del color deseado.');
      return;
    }

    if (!isVerified) {
      alert('Por favor, verifica que el análisis es correcto antes de continuar.');
      return;
    }

    updateDesiredColor(images, analysis, analysis.technique);
    router.push(`/formula/step3?clientId=${clientId}`);
  };

  const renderColorIndicator = (color?: string) => {
    if (!color) return null;
    return (
      <View
        style={[
          styles.colorIndicator,
          { backgroundColor: color.startsWith('#') ? color : '#888' },
        ]}
      />
    );
  };

  const renderReflectionIntensityIndicator = (intensity: string) => {
    const intensities = [
      { value: 'sutil', label: 'Sutil', bars: 1 },
      { value: 'medio', label: 'Medio', bars: 2 },
      { value: 'intenso', label: 'Intenso', bars: 3 },
    ];

    return (
      <View style={styles.visualSelectContainer}>
        {intensities.map((item) => (
          <TouchableOpacity
            key={item.value}
            style={[
              styles.visualSelectOption,
              intensity === item.value && styles.visualSelectOptionActive,
            ]}
            onPress={() => {
              if (analysis) {
                setAnalysis({
                  ...analysis,
                  reflectionIntensity: item.value as any,
                });
              }
            }}
          >
            <View style={styles.intensityBarsContainer}>
              {Array.from({ length: 3 }).map((_, i) => (
                <View
                  key={i}
                  style={[
                    styles.intensityBar,
                    i < item.bars && intensity === item.value && styles.intensityBarActive,
                    i < item.bars && intensity !== item.value && styles.intensityBarInactive,
                  ]}
                />
              ))}
            </View>
            <Text
              style={[
                styles.visualSelectLabel,
                intensity === item.value && styles.visualSelectLabelActive,
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderResultTypeIndicator = (resultType: string) => {
    const resultTypes = [
      { value: 'natural', label: 'Natural', icon: '🌿' },
      { value: 'vibrante', label: 'Vibrante', icon: '✨' },
      { value: 'fantasía', label: 'Fantasía', icon: '🌈' },
    ];

    return (
      <View style={styles.visualSelectContainer}>
        {resultTypes.map((item) => (
          <TouchableOpacity
            key={item.value}
            style={[
              styles.visualSelectOption,
              resultType === item.value && styles.visualSelectOptionActive,
            ]}
            onPress={() => {
              if (analysis) {
                setAnalysis({
                  ...analysis,
                  resultType: item.value as any,
                });
              }
            }}
          >
            <Text style={styles.visualSelectIcon}>{item.icon}</Text>
            <Text
              style={[
                styles.visualSelectLabel,
                resultType === item.value && styles.visualSelectLabelActive,
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderColorDepthIndicator = (depth: string) => {
    const depths = [
      { value: 'sutil', label: 'Sutil', opacity: 0.3 },
      { value: 'medio', label: 'Medio', opacity: 0.6 },
      { value: 'profundo', label: 'Profundo', opacity: 1 },
    ];

    return (
      <View style={styles.visualSelectContainer}>
        {depths.map((item) => (
          <TouchableOpacity
            key={item.value}
            style={[
              styles.visualSelectOption,
              depth === item.value && styles.visualSelectOptionActive,
            ]}
            onPress={() => {
              if (analysis) {
                setAnalysis({
                  ...analysis,
                  colorDepth: item.value as any,
                });
              }
            }}
          >
            <View
              style={[
                styles.depthCircle,
                {
                  backgroundColor: Colors.light.primary,
                  opacity: item.opacity,
                },
              ]}
            />
            <Text
              style={[
                styles.visualSelectLabel,
                depth === item.value && styles.visualSelectLabelActive,
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderZoneAnalysis = (zone: DesiredColorZone | undefined, zoneKey: 'roots' | 'mids' | 'ends', zoneName: string) => {
    if (!zone) return null;

    return (
      <View style={styles.zoneContainer}>
        <View style={styles.fieldRow}>
          <Text style={styles.fieldLabel}>Nivel deseado</Text>
          <View style={styles.fieldValueContainer}>
            <TextInput
              style={styles.fieldInput}
              value={zone.level.toString()}
              onChangeText={(text) => {
                if (analysis) {
                  setAnalysis({
                    ...analysis,
                    [zoneKey]: { ...zone, level: parseInt(text) || 1 },
                  });
                }
              }}
              keyboardType="numeric"
            />
            <Text style={styles.fieldUnit}>/10</Text>
          </View>
        </View>

        <TouchableOpacity
          style={styles.selectField}
          onPress={() => {
            setModalTarget({ zone: zoneKey, field: 'tone' });
            setShowToneModal(true);
          }}
          activeOpacity={0.7}
        >
          <Text style={styles.selectFieldLabel}>Tono deseado</Text>
          <View style={styles.selectFieldValue}>
            {renderColorIndicator(zone.toneColor)}
            <Text style={styles.selectFieldText}>{zone.tone || 'Seleccionar'}</Text>
            <ChevronDown color={Colors.light.textSecondary} size={20} />
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.selectField}
          onPress={() => {
            setModalTarget({ zone: zoneKey, field: 'reflection' });
            setShowReflectionModal(true);
          }}
          activeOpacity={0.7}
        >
          <Text style={styles.selectFieldLabel}>Reflejo deseado</Text>
          <View style={styles.selectFieldValue}>
            {renderColorIndicator(zone.reflectionColor)}
            <Text style={styles.selectFieldText}>{zone.reflection || 'Seleccionar'}</Text>
            <ChevronDown color={Colors.light.textSecondary} size={20} />
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const renderCurrentColorSummary = () => {
    if (!formulaData.currentColorAnalysis) return null;

    const current = formulaData.currentColorAnalysis;

    return (
      <View style={styles.summaryContainer}>
        <View style={styles.summaryHeader}>
          <Text style={styles.summaryTitle}>📋 Resumen del Color Actual</Text>
        </View>
        <View style={styles.summaryContent}>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Tono predominante</Text>
            <View style={styles.summaryValue}>
              {renderColorIndicator(current.generalCharacteristics.predominantToneColor)}
              <Text style={styles.summaryText}>{current.generalCharacteristics.predominantTone}</Text>
            </View>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Reflejo predominante</Text>
            <View style={styles.summaryValue}>
              {renderColorIndicator(current.generalCharacteristics.predominantReflectionColor)}
              <Text style={styles.summaryText}>{current.generalCharacteristics.predominantReflection}</Text>
            </View>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Nivel raíces</Text>
            <Text style={styles.summaryText}>{current.roots.level}/10</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Canas</Text>
            <Text style={styles.summaryText}>{current.grayAnalysis.percentage}%</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Estado general</Text>
            <Text style={styles.summaryText}>{current.roots.state}</Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <ProgressIndicator currentStep={3} totalSteps={6} />
      <ScrollView
        contentContainerStyle={[styles.content, { paddingBottom: insets.bottom + 100 }]}
        showsVerticalScrollIndicator={false}
      >
        <Text style={styles.title}>Definir Color Deseado</Text>
        <Text style={styles.subtitle}>Sube fotos de referencia del color objetivo</Text>

        {renderCurrentColorSummary()}

        <PhotoGuidance />

        {isUploading && (
          <View style={styles.uploadingBanner}>
            <ActivityIndicator color={Colors.light.primary} />
            <Text style={styles.uploadingText}>Procesando fotos...</Text>
          </View>
        )}

        {images.length === 0 ? (
          <View style={styles.photoOptionsContainer}>
            <TouchableOpacity style={styles.photoOptionButton} onPress={takePhoto} disabled={isUploading}>
              <View style={styles.photoOptionIcon}>
                <Camera color={Colors.light.primary} size={32} />
              </View>
              <Text style={styles.photoOptionTitle}>Tomar foto</Text>
              <Text style={styles.photoOptionDesc}>Usa la cámara</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.photoOptionButton} onPress={pickImages} disabled={isUploading}>
              <View style={styles.photoOptionIcon}>
                <ImageIcon color={Colors.light.primary} size={32} />
              </View>
              <Text style={styles.photoOptionTitle}>Subir fotos</Text>
              <Text style={styles.photoOptionDesc}>Desde galería</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.imagesGrid}>
            {images.map((uri, index) => (
              <View key={index} style={styles.imageContainer}>
                <Image source={{ uri }} style={styles.image} />
                <TouchableOpacity
                  style={styles.removeButton}
                  onPress={() => removeImage(index)}
                >
                  <X color={Colors.light.background} size={16} />
                </TouchableOpacity>
              </View>
            ))}
            
            {images.length < 6 && (
              <View style={styles.addMoreContainer}>
                <TouchableOpacity style={styles.addMoreButton} onPress={takePhoto} disabled={isUploading}>
                  <Camera color={Colors.light.primary} size={20} />
                  <Text style={styles.addMoreText}>Tomar</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.addMoreButton} onPress={pickImages} disabled={isUploading}>
                  <ImageIcon color={Colors.light.primary} size={20} />
                  <Text style={styles.addMoreText}>Agregar</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        )}

        {images.length > 0 && !analysis && (
          <TouchableOpacity
            style={[styles.analyzeButton, isAnalyzing && styles.analyzeButtonDisabled]}
            onPress={analyzeImages}
            disabled={isAnalyzing}
          >
            {isAnalyzing ? (
              <>
                <ActivityIndicator color={Colors.light.background} />
                <Text style={styles.analyzeButtonText}>Analizando...</Text>
              </>
            ) : (
              <Text style={styles.analyzeButtonText}>Analizar con IA</Text>
            )}
          </TouchableOpacity>
        )}

        {analysis && (
          <View style={styles.analysisContainer}>
            <View style={styles.analysisHeaderSection}>
              <Text style={styles.analysisTitle}>Análisis del Color Deseado</Text>
              <Text style={styles.analysisSubtitle}>Revisa y edita la información si es necesario</Text>
            </View>

            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => toggleSection('general')}
              activeOpacity={0.7}
            >
              <Text style={styles.sectionTitle}>Características Generales</Text>
              {expandedSections.has('general') ? (
                <ChevronUp color={Colors.light.textSecondary} size={20} />
              ) : (
                <ChevronDown color={Colors.light.textSecondary} size={20} />
              )}
            </TouchableOpacity>

            {expandedSections.has('general') && (
              <View style={styles.sectionContent}>
                <View style={styles.fieldRow}>
                  <Text style={styles.fieldLabel}>Nivel de tono deseado</Text>
                  <View style={styles.fieldValueContainer}>
                    <TextInput
                      style={styles.fieldInput}
                      value={analysis.level.toString()}
                      onChangeText={(text) => setAnalysis({ ...analysis, level: parseInt(text) || 1 })}
                      keyboardType="numeric"
                    />
                    <Text style={styles.fieldUnit}>/10</Text>
                  </View>
                </View>

                <TouchableOpacity
                  style={styles.selectField}
                  onPress={() => {
                    setModalTarget({ zone: 'general', field: 'tone' });
                    setShowToneModal(true);
                  }}
                  activeOpacity={0.7}
                >
                  <Text style={styles.selectFieldLabel}>Tono base deseado</Text>
                  <View style={styles.selectFieldValue}>
                    {renderColorIndicator(sanitizeHexColor(analysis.toneColor))}
                    <Text style={styles.selectFieldText}>{sanitizeTextForDisplay(analysis.tone) || 'Seleccionar'}</Text>
                    <ChevronDown color={Colors.light.textSecondary} size={20} />
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.selectField}
                  onPress={() => {
                    setModalTarget({ zone: 'general', field: 'reflection' });
                    setShowReflectionModal(true);
                  }}
                  activeOpacity={0.7}
                >
                  <Text style={styles.selectFieldLabel}>Reflejo/matiz deseado</Text>
                  <View style={styles.selectFieldValue}>
                    {renderColorIndicator(sanitizeHexColor(analysis.reflectionColor))}
                    <Text style={styles.selectFieldText}>{sanitizeTextForDisplay(analysis.reflection) || 'Seleccionar'}</Text>
                    <ChevronDown color={Colors.light.textSecondary} size={20} />
                  </View>
                </TouchableOpacity>

                <View style={styles.fieldColumn}>
                  <Text style={styles.fieldLabel}>Intensidad del reflejo</Text>
                  {renderReflectionIntensityIndicator(analysis.reflectionIntensity)}
                </View>

                <View style={styles.fieldRow}>
                  <Text style={styles.fieldLabel}>Cobertura de canas</Text>
                  <View style={styles.fieldValueContainer}>
                    <TextInput
                      style={styles.fieldInput}
                      value={analysis.grayCoverage.toString()}
                      onChangeText={(text) => setAnalysis({ ...analysis, grayCoverage: parseInt(text) || 0 })}
                      keyboardType="numeric"
                    />
                    <Text style={styles.fieldUnit}>%</Text>
                  </View>
                </View>

                <View style={styles.fieldColumn}>
                  <Text style={styles.fieldLabel}>Tipo de resultado</Text>
                  {renderResultTypeIndicator(analysis.resultType)}
                </View>

                <TouchableOpacity
                  style={styles.selectField}
                  onPress={() => setShowTechniqueModal(true)}
                  activeOpacity={0.7}
                >
                  <Text style={styles.selectFieldLabel}>Técnica de aplicación</Text>
                  <View style={styles.selectFieldValue}>
                    <Text style={styles.selectFieldText}>{analysis.technique || 'Seleccionar'}</Text>
                    <ChevronDown color={Colors.light.textSecondary} size={20} />
                  </View>
                </TouchableOpacity>

                <View style={styles.fieldColumn}>
                  <Text style={styles.fieldLabel}>Profundidad del color</Text>
                  {renderColorDepthIndicator(analysis.colorDepth)}
                </View>
              </View>
            )}

            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => toggleSection('roots')}
              activeOpacity={0.7}
            >
              <Text style={styles.sectionTitle}>Color Deseado en Raíces</Text>
              {expandedSections.has('roots') ? (
                <ChevronUp color={Colors.light.textSecondary} size={20} />
              ) : (
                <ChevronDown color={Colors.light.textSecondary} size={20} />
              )}
            </TouchableOpacity>

            {expandedSections.has('roots') && (
              <View style={styles.sectionContent}>
                {renderZoneAnalysis(analysis.roots, 'roots', 'Raíces')}
              </View>
            )}

            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => toggleSection('mids')}
              activeOpacity={0.7}
            >
              <Text style={styles.sectionTitle}>Color Deseado en Medios</Text>
              {expandedSections.has('mids') ? (
                <ChevronUp color={Colors.light.textSecondary} size={20} />
              ) : (
                <ChevronDown color={Colors.light.textSecondary} size={20} />
              )}
            </TouchableOpacity>

            {expandedSections.has('mids') && (
              <View style={styles.sectionContent}>
                {renderZoneAnalysis(analysis.mids, 'mids', 'Medios')}
              </View>
            )}

            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => toggleSection('ends')}
              activeOpacity={0.7}
            >
              <Text style={styles.sectionTitle}>Color Deseado en Puntas</Text>
              {expandedSections.has('ends') ? (
                <ChevronUp color={Colors.light.textSecondary} size={20} />
              ) : (
                <ChevronDown color={Colors.light.textSecondary} size={20} />
              )}
            </TouchableOpacity>

            {expandedSections.has('ends') && (
              <View style={styles.sectionContent}>
                {renderZoneAnalysis(analysis.ends, 'ends', 'Puntas')}
              </View>
            )}

            <View style={styles.sectionContent}>
              <View style={styles.fieldRow}>
                <Text style={styles.fieldLabel}>Notas adicionales</Text>
                <TextInput
                  style={[styles.fieldInput, styles.fieldInputExpanded, styles.fieldInputMultiline]}
                  value={analysis.notes || ''}
                  onChangeText={(text) => setAnalysis({ ...analysis, notes: text })}
                  multiline
                  numberOfLines={2}
                  placeholder="Observaciones o detalles adicionales"
                  placeholderTextColor={Colors.light.textLight}
                />
              </View>
            </View>

            <TouchableOpacity
              style={styles.verificationSection}
              onPress={() => setIsVerified(!isVerified)}
              activeOpacity={0.7}
            >
              <View style={[styles.verificationCheckbox, isVerified && styles.verificationCheckboxActive]}>
                {isVerified && <Check color={Colors.light.background} size={20} strokeWidth={3} />}
              </View>
              <View style={styles.verificationContent}>
                <Text style={styles.verificationTitle}>He verificado el análisis</Text>
                <Text style={styles.verificationSubtitle}>La información es correcta y puedo continuar</Text>
              </View>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      <View style={[styles.footer, { paddingBottom: insets.bottom + 16 }]}>
        <TouchableOpacity
          style={[styles.nextButton, (!analysis || images.length === 0 || !isVerified) && styles.nextButtonDisabled]}
          onPress={handleSave}
          disabled={!analysis || images.length === 0 || !isVerified}
        >
          <Text style={styles.nextButtonText}>Continuar</Text>
          <ChevronRight color={Colors.light.background} size={20} />
        </TouchableOpacity>
      </View>

      <Modal
        visible={showToneModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowToneModal(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setShowToneModal(false)}
        >
          <Pressable style={[styles.modalContent, { paddingBottom: insets.bottom + 20 }]} onPress={e => e.stopPropagation()}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Seleccionar tono</Text>
              <TouchableOpacity
                onPress={() => setShowToneModal(false)}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <X color={Colors.light.textSecondary} size={24} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalScroll} showsVerticalScrollIndicator={false}>
              {toneOptions.map((option) => {
                let isSelected = false;
                if (modalTarget?.zone && modalTarget.zone !== 'general' && analysis?.[modalTarget.zone]) {
                  isSelected = analysis[modalTarget.zone]?.tone === option.value;
                } else {
                  isSelected = analysis?.tone === option.value;
                }
                return (
                  <TouchableOpacity
                    key={option.value}
                    style={[styles.modalOption, isSelected && styles.modalOptionSelected]}
                    onPress={() => {
                      if (analysis) {
                        if (modalTarget?.zone && modalTarget.zone !== 'general') {
                          const zone = analysis[modalTarget.zone];
                          setAnalysis({
                            ...analysis,
                            [modalTarget.zone]: {
                              ...zone,
                              tone: option.value,
                              toneColor: option.color,
                            },
                          });
                        } else {
                          setAnalysis({
                            ...analysis,
                            tone: option.value,
                            toneColor: option.color,
                          });
                        }
                      }
                      setShowToneModal(false);
                    }}
                    activeOpacity={0.7}
                  >
                    <View style={styles.modalOptionContent}>
                      <View style={[styles.toneCircle, { backgroundColor: option.color }]} />
                      <Text style={[styles.modalOptionText, isSelected && styles.modalOptionTextSelected]}>
                        {option.label}
                      </Text>
                    </View>
                    {isSelected && <Check color={Colors.light.primary} size={20} />}
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </Pressable>
        </Pressable>
      </Modal>

      <Modal
        visible={showReflectionModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowReflectionModal(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setShowReflectionModal(false)}
        >
          <Pressable style={[styles.modalContent, { paddingBottom: insets.bottom + 20 }]} onPress={e => e.stopPropagation()}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Seleccionar reflejo/matiz</Text>
              <TouchableOpacity
                onPress={() => setShowReflectionModal(false)}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <X color={Colors.light.textSecondary} size={24} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalScroll} showsVerticalScrollIndicator={false}>
              {reflectionOptions.map((option) => {
                let isSelected = false;
                if (modalTarget?.zone && modalTarget.zone !== 'general' && analysis?.[modalTarget.zone]) {
                  isSelected = analysis[modalTarget.zone]?.reflection === option.value;
                } else {
                  isSelected = analysis?.reflection === option.value;
                }
                return (
                  <TouchableOpacity
                    key={option.value}
                    style={[styles.modalOption, isSelected && styles.modalOptionSelected]}
                    onPress={() => {
                      if (analysis) {
                        if (modalTarget?.zone && modalTarget.zone !== 'general') {
                          const zone = analysis[modalTarget.zone];
                          setAnalysis({
                            ...analysis,
                            [modalTarget.zone]: {
                              ...zone,
                              reflection: option.value,
                              reflectionColor: option.color,
                            },
                          });
                        } else {
                          setAnalysis({
                            ...analysis,
                            reflection: option.value,
                            reflectionColor: option.color,
                          });
                        }
                      }
                      setShowReflectionModal(false);
                    }}
                    activeOpacity={0.7}
                  >
                    <View style={styles.modalOptionContent}>
                      <View style={[styles.toneCircle, { backgroundColor: option.color }]} />
                      <Text style={[styles.modalOptionText, isSelected && styles.modalOptionTextSelected]}>
                        {option.label}
                      </Text>
                    </View>
                    {isSelected && <Check color={Colors.light.primary} size={20} />}
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </Pressable>
        </Pressable>
      </Modal>

      <Modal
        visible={showTechniqueModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowTechniqueModal(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setShowTechniqueModal(false)}
        >
          <Pressable style={[styles.modalContent, { paddingBottom: insets.bottom + 20 }]} onPress={e => e.stopPropagation()}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Seleccionar técnica</Text>
              <TouchableOpacity
                onPress={() => setShowTechniqueModal(false)}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <X color={Colors.light.textSecondary} size={24} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalScroll} showsVerticalScrollIndicator={false}>
              {techniqueOptions.map((option) => {
                const isSelected = analysis?.technique === option.value;
                return (
                  <TouchableOpacity
                    key={option.value}
                    style={[styles.modalOption, isSelected && styles.modalOptionSelected]}
                    onPress={() => {
                      if (analysis) {
                        setAnalysis({
                          ...analysis,
                          technique: option.value,
                        });
                      }
                      setShowTechniqueModal(false);
                    }}
                    activeOpacity={0.7}
                  >
                    <View style={styles.techniqueOptionContent}>
                      <Text style={[styles.techniqueOptionTitle, isSelected && styles.modalOptionTextSelected]}>
                        {option.label}
                      </Text>
                      <Text style={styles.techniqueOptionDesc}>{option.description}</Text>
                    </View>
                    {isSelected && <Check color={Colors.light.primary} size={20} />}
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </Pressable>
        </Pressable>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: '800' as const,
    color: Colors.light.text,
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    marginBottom: 20,
    fontWeight: '500' as const,
  },
  summaryContainer: {
    backgroundColor: '#F8FAFC',
    borderRadius: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
    overflow: 'hidden',
  },
  summaryHeader: {
    padding: 16,
    backgroundColor: '#EEF2FF',
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '700' as const,
    color: Colors.light.text,
  },
  summaryContent: {
    padding: 16,
    gap: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    flex: 1,
  },
  summaryValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  summaryText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 14,
    borderRadius: 12,
    marginBottom: 16,
  },
  tipsHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  tipsHeaderText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  tipsContainer: {
    backgroundColor: '#F8F9FB',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderLeftWidth: 3,
    borderLeftColor: Colors.light.primary,
  },
  tipTitle: {
    fontSize: 13,
    fontWeight: '600' as const,
    color: Colors.light.text,
    marginBottom: 10,
  },
  tipsList: {
    gap: 6,
  },
  tipItem: {
    fontSize: 13,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  photoOptionsContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 20,
  },
  photoOptionButton: {
    flex: 1,
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
    gap: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  photoOptionIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: Colors.light.background,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  photoOptionTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  photoOptionDesc: {
    fontSize: 13,
    color: Colors.light.textSecondary,
  },
  imagesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 20,
  },
  imageContainer: {
    position: 'relative',
    width: '30%',
    aspectRatio: 1,
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  removeButton: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: Colors.light.text,
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addMoreContainer: {
    width: '30%',
    aspectRatio: 1,
    gap: 6,
  },
  addMoreButton: {
    flex: 1,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.light.border,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  addMoreText: {
    fontSize: 10,
    color: Colors.light.primary,
    fontWeight: '600' as const,
  },
  analyzeButton: {
    backgroundColor: Colors.light.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    borderRadius: 16,
    gap: 10,
    marginBottom: 20,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 4,
  },
  analyzeButtonDisabled: {
    opacity: 0.5,
  },
  analyzeButtonText: {
    color: Colors.light.background,
    fontSize: 17,
    fontWeight: '700' as const,
    letterSpacing: -0.2,
  },
  analysisContainer: {
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
    padding: 0,
    overflow: 'hidden',
  },
  analysisHeaderSection: {
    padding: 24,
    backgroundColor: '#F8FAFC',
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  analysisTitle: {
    fontSize: 22,
    fontWeight: '700' as const,
    color: Colors.light.text,
    marginBottom: 4,
  },
  analysisSubtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 18,
    backgroundColor: Colors.light.background,
    borderTopWidth: 1,
    borderTopColor: '#F1F5F9',
  },
  sectionTitle: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.text,
    letterSpacing: -0.2,
  },
  sectionContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    gap: 20,
    backgroundColor: Colors.light.background,
  },
  fieldRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 12,
  },
  fieldLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    flex: 1,
  },
  fieldValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
    justifyContent: 'flex-end',
  },
  fieldInput: {
    fontSize: 15,
    fontWeight: '500' as const,
    color: Colors.light.text,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    paddingVertical: 6,
    paddingHorizontal: 8,
    minWidth: 80,
    textAlign: 'right',
  },
  fieldInputExpanded: {
    flex: 1,
  },
  fieldInputMultiline: {
    textAlign: 'left',
    minHeight: 60,
  },
  fieldUnit: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  fieldColumn: {
    gap: 12,
  },
  colorIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  selectField: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  selectFieldLabel: {
    fontSize: 13,
    fontWeight: '500' as const,
    color: Colors.light.textSecondary,
    marginBottom: 8,
  },
  selectFieldValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  selectFieldText: {
    fontSize: 16,
    fontWeight: '500' as const,
    color: Colors.light.text,
    flex: 1,
  },
  visualSelectContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  visualSelectOption: {
    flex: 1,
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    borderWidth: 2,
    borderColor: Colors.light.border,
    minHeight: 80,
  },
  visualSelectOptionActive: {
    borderColor: Colors.light.primary,
    backgroundColor: '#EEF2FF',
  },
  visualSelectLabel: {
    fontSize: 13,
    fontWeight: '500' as const,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  visualSelectLabelActive: {
    color: Colors.light.primary,
    fontWeight: '600' as const,
  },
  visualSelectIcon: {
    fontSize: 24,
  },
  intensityBarsContainer: {
    flexDirection: 'row',
    gap: 3,
    height: 30,
    alignItems: 'flex-end',
  },
  intensityBar: {
    width: 8,
    height: '33%',
    backgroundColor: Colors.light.border,
    borderRadius: 2,
  },
  intensityBarActive: {
    backgroundColor: Colors.light.primary,
    height: '100%',
  },
  intensityBarInactive: {
    backgroundColor: '#C7D2FE',
    height: '100%',
  },
  depthCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  zoneContainer: {
    gap: 16,
  },
  verificationSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    padding: 20,
    marginHorizontal: 20,
    marginTop: 24,
    marginBottom: 20,
    backgroundColor: '#F0F9FF',
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#E0F2FE',
  },
  verificationCheckbox: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.background,
    alignItems: 'center',
    justifyContent: 'center',
  },
  verificationCheckboxActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  verificationContent: {
    flex: 1,
  },
  verificationTitle: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.text,
    marginBottom: 2,
  },
  verificationSubtitle: {
    fontSize: 13,
    color: Colors.light.textSecondary,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 20,
    paddingTop: 16,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 8,
  },
  nextButton: {
    backgroundColor: Colors.light.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    borderRadius: 16,
    gap: 10,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 4,
  },
  nextButtonDisabled: {
    opacity: 0.5,
  },
  nextButtonText: {
    color: Colors.light.background,
    fontSize: 17,
    fontWeight: '700' as const,
    letterSpacing: -0.2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700' as const,
    color: Colors.light.text,
  },
  modalScroll: {
    paddingHorizontal: 20,
    paddingTop: 12,
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    backgroundColor: Colors.light.background,
  },
  modalOptionSelected: {
    backgroundColor: '#F0F4FF',
  },
  modalOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  modalOptionText: {
    fontSize: 16,
    fontWeight: '500' as const,
    color: Colors.light.text,
  },
  modalOptionTextSelected: {
    color: Colors.light.primary,
    fontWeight: '600' as const,
  },
  toneCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  techniqueOptionContent: {
    flex: 1,
    gap: 4,
  },
  techniqueOptionTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  techniqueOptionDesc: {
    fontSize: 13,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },
  uploadingBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    backgroundColor: '#EEF2FF',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  uploadingText: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.primary,
  },
});
