/**
 * AnonymizerContext
 * Provides face anonymization functionality across the app
 */

import React, { useCallback, useState } from 'react';
import createContextHook from '@nkzw/create-context-hook';
import { anonymizeFaces, cleanupAnonymizedFiles } from '@/lib/faceAnonymizer';
import { Alert } from 'react-native';

interface AnonymizerContextType {
  isAnonymizing: boolean;
  anonymizeImages: (imageUris: string[]) => Promise<string[]>;
  cleanupImages: (imageUris: string[]) => Promise<void>;
}

export const [AnonymizerProvider, useAnonymizer] = createContextHook<AnonymizerContextType>(() => {
  const [isAnonymizing, setIsAnonymizing] = useState(false);

  const anonymizeImages = useCallback(async (imageUris: string[]): Promise<string[]> => {
    if (!imageUris || imageUris.length === 0) {
      return [];
    }

    setIsAnonymizing(true);

    try {
      const result = await anonymizeFaces(imageUris);

      if (!result.success) {
        // Warn user but don't block - return original images
        console.warn('Face anonymization failed, using original images');

        // Optional: Show user warning
        if (result.error?.includes('Edge Function')) {
          Alert.alert(
            'Privacy Notice',
            'Face blurring service is temporarily unavailable. Images will be processed without face anonymization.',
            [{ text: 'OK' }]
          );
        }
      }

      console.log(`Images processed via ${result.method} method`);

      if (result.facesDetected !== undefined) {
        console.log(`Detected and blurred ${result.facesDetected} faces`);
      }

      return result.imageUris;

    } catch (error) {
      console.error('Anonymization error:', error);
      // Return original images on error
      return imageUris;
    } finally {
      setIsAnonymizing(false);
    }
  }, []);

  const cleanupImages = useCallback(async (imageUris: string[]) => {
    try {
      await cleanupAnonymizedFiles(imageUris);
    } catch (error) {
      console.error('Cleanup error:', error);
    }
  }, []);

  return {
    isAnonymizing,
    anonymizeImages,
    cleanupImages
  };
});