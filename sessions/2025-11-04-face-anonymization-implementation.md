# Face Anonymization Implementation - VisionSafetyError Fix
**Fecha**: 2025-11-04
**Estado**: ✅ Edge Function funcional, ❌ Bug en análisis de OpenAI

---

## Contexto

### Problema Original
OpenAI Vision API devolvía `VisionSafetyError` al analizar fotos de cabello que contenían caras visibles, bloqueando completamente el análisis de color.

### Solución Implementada
Sistema de anonymización de caras con dos capas:
1. **Server-side** (Edge Function): Anonymización con ImageScript
2. **Client-side** (Fallback): Filtro de privacidad local con expo-image-manipulator

---

## Implementación Completada

### Archivos Creados/Modificados

**Edge Function** (`supabase/functions/anonymize-faces/index.ts`):
- v6 desplegada en producción
- Métodos: blackout (default), pixelate, blur
- Procesa top 40%, preserva bottom 60% para análisis de cabello
- Fix crítico: ImageScript usa coordenadas 1-indexed (x=1, y=1 como inicio)
- Fix crítico: Chunked base64 encoding para imágenes grandes (32KB chunks)

**Client Library** (`lib/faceAnonymizer.ts`):
- `anonymizeFaces()`: Intenta server-side, fallback a local
- `serverSideAnonymization()`: Llama Edge Function con base64
- `localPrivacyFilter()`: Crop a bottom 60% + pixelation extrema

**Context Provider** (`contexts/AnonymizerContext.tsx`):
- `useAnonymizer()` hook con `anonymizeImages()` y `cleanupImages()`
- State: `isAnonymizing`

**Integración**:
- `app/(app)/(tabs)/chat.tsx` - Chat con imágenes
- `app/(app)/formula/step1.tsx` - Análisis color actual
- `app/(app)/formula/step2.tsx` - Análisis color deseado
- `app/(app)/formula/step5.tsx` - Generación de fórmula
- `components/Providers.tsx` - AnonymizerProvider wrapper

### Commits Realizados

```
cb1df85 - fix: Correct import statement for createContextHook
11b5778 - fix: Critical fixes for face anonymization
8691daf - fix: Use legacy FileSystem API to avoid deprecation warnings
1ad0d0e - fix: Use aggressive local privacy filter to prevent VisionSafetyError
5fefe06 - feat: Deploy production Edge Function with blackout method
24a21bf - fix: CRITICAL - Use 1-indexed coordinates for ImageScript in Edge Function
bcb9055 - fix: Use chunked base64 encoding for large images in Edge Function
```

**Branch**: `feature/face-anonymization` (pusheada a GitHub)

---

## Bugs Resueltos

### Bug #1: RangeError en Edge Function v5
**Error**: `RangeError: Tried referencing a pixel outside of the images boundaries: (x=0)<1`

**Causa**: ImageScript usa coordenadas 1-indexed (pixels empiezan en x=1, y=1, no en 0,0)

**Solución**: Cambiar todos los loops de pixel:
```typescript
// ❌ BEFORE (0-indexed)
for (let y = 0; y < blackoutHeight; y++) {
  for (let x = 0; x < width; x++) {
    image.setPixelAt(x, y, 0x000000FF);
  }
}

// ✅ AFTER (1-indexed)
for (let y = 1; y <= blackoutHeight; y++) {
  for (let x = 1; x <= width; x++) {
    image.setPixelAt(x, y, 0x000000FF);
  }
}
```

### Bug #2: Stack Overflow en Edge Function v5
**Error**: `POST | 500` - Maximum call stack size exceeded

**Causa**: `btoa(String.fromCharCode(...buffer))` intenta expandir todo el array en la pila de llamadas cuando el buffer es muy grande (>100KB)

**Solución**: Procesar en chunks de 32KB:
```typescript
function bufferToBase64(buffer: Uint8Array): string {
  const chunkSize = 32768; // 32KB chunks
  let binary = '';

  for (let i = 0; i < buffer.length; i += chunkSize) {
    const chunk = buffer.slice(i, i + chunkSize);
    binary += String.fromCharCode(...chunk);
  }

  return btoa(binary);
}
```

---

## Estado Actual - Testing

### ✅ Edge Function FUNCIONAL
**Logs de producción**:
```
✅ POST | 200 | anonymize-faces | v6 | execution_time_ms: 1513
✅ Edge Function processed 1 images
✅ Server-side anonymization successful (server)
✅ base64 length: 259280
```

**Verificado**:
- ✅ NO más RangeError
- ✅ NO más 500 errors
- ✅ NO más fallback a método local
- ✅ Imágenes procesadas correctamente
- ✅ Archivo JPEG guardado correctamente (anonymized_*.jpg)

### ❌ BUG PENDIENTE: OpenAI devuelve "[Image #1]"

**Síntoma**: OpenAI Vision API devuelve el texto literal `[Image #1]` en lugar del análisis de cabello

**Logs de la app**:
```
LOG  Images processed via server method
LOG  [AIClient] Successfully processed image 1, base64 length: 259280
LOG  [AIClient] Success! Cost: $0.0078, Latency: 2452ms
```

**Observaciones**:
- La imagen anonymizada se guarda correctamente (259KB base64)
- OpenAI recibe la imagen (cobra $0.0078 por procesamiento)
- OpenAI devuelve respuesta exitosa PERO con "[Image #1]" en lugar de análisis

**Hipótesis principal**: El blackout del 40% superior (barra negra sólida) está interfiriendo con la capacidad de OpenAI Vision API de analizar la imagen, causando que devuelva un placeholder en lugar del análisis real.

**Hipótesis alternativas**:
1. OpenAI está rechazando la imagen por contenido (aunque no debería, las caras están anonymizadas)
2. La data URL está mal formada (poco probable, OpenAI la acepta y procesa)
3. El base64 tiene corrupción sutil que OpenAI detecta (poco probable, tamaño correcto)

---

## Soluciones Propuestas para "[Image #1]" Bug

### Opción 1: Crop en lugar de Blackout (RECOMENDADA)
**Qué hacer**: Modificar Edge Function para hacer crop directamente en lugar de blackout
- Enviar solo el 60% inferior de la imagen (donde está el cabello)
- Eliminar completamente el 40% superior (caras)
- Esto es lo que hace el fallback local y sabemos que funciona

**Ventajas**:
- OpenAI recibe una imagen "normal" sin áreas negras sospechosas
- Reduce tamaño de imagen → menor costo
- Mismo nivel de privacidad (caras eliminadas completamente)

**Desventajas**:
- Pierde contexto visual del 40% superior

**Código necesario**:
```typescript
// En Edge Function, reemplazar applyBlackout con applyCrop
async function applyCrop(imageBuffer: Uint8Array, preserveRatio: number): Promise<Uint8Array> {
  const image = await Image.decode(imageBuffer);
  const width = image.width;
  const height = image.height;

  const cropStartY = Math.floor(height * (1 - preserveRatio)) + 1; // +1 for 1-indexed
  const cropHeight = Math.floor(height * preserveRatio);

  // Create new image with only bottom portion
  const croppedImage = new Image(width, cropHeight);

  for (let y = 1; y <= cropHeight; y++) {
    for (let x = 1; x <= width; x++) {
      const sourceY = cropStartY + y - 1;
      croppedImage.setPixelAt(x, y, image.getPixelAt(x, sourceY));
    }
  }

  return await croppedImage.encodeJPEG(90);
}
```

### Opción 2: Reducir Área de Blackout
**Qué hacer**: Cambiar de 40% blackout a 30% blackout (preservar 70% en lugar de 60%)

**Ventajas**:
- Menos invasivo visualmente
- Mantiene más contexto de la imagen

**Desventajas**:
- Puede no eliminar todas las caras (caras muy grandes)
- OpenAI podría seguir viendo la barra negra como sospechosa

**Código**:
```typescript
// En lib/faceAnonymizer.ts, cambiar preserveRatio de 0.6 a 0.7
const { data, error } = await supabase.functions.invoke('anonymize-faces', {
  body: {
    images: base64Images,
    method,
    preserveRatio: 0.7 // Preserve bottom 70% (blackout top 30%)
  }
});
```

### Opción 3: Usar Pixelation en lugar de Blackout
**Qué hacer**: Cambiar método default de 'blackout' a 'pixelate'

**Ventajas**:
- Visualmente menos agresivo
- Mantiene "información" visual (aunque pixelada)
- OpenAI podría procesarlo mejor

**Desventajas**:
- Menos privacidad (patrones de píxeles pueden revelar formas)
- OpenAI podría seguir detectando caras pixeladas

**Código**:
```typescript
// En lib/faceAnonymizer.ts, cambiar método default
export async function anonymizeFaces(
  imageUris: string[],
  method: 'blackout' | 'pixelate' | 'blur' = 'pixelate' // Changed from 'blackout'
): Promise<AnonymizationResult> {
```

---

## Testing Realizado

### Test 1: Edge Function v6
**Input**: 1 imagen de cabello (896x1195, ~200KB)
**Output**:
```
✅ POST | 200 (1513ms)
✅ Image processed successfully
✅ File saved: anonymized_1762245673039_0.jpg (259KB base64)
```

### Test 2: Chat Analysis
**Input**: Imagen anonymizada enviada a OpenAI via ai-proxy
**Output**:
```
❌ OpenAI devuelve: "[Image #1]"
✅ Cost: $0.0078 (imagen procesada)
✅ Latency: 2452ms
```

---

## Arquitectura Técnica

### Flow de Anonymización

```
User Upload Image
    ↓
processMultipleImages() [resize to 800px width]
    ↓
anonymizeImages() [AnonymizerContext]
    ↓
├─→ serverSideAnonymization()
│       ↓
│   Edge Function (anonymize-faces v6)
│       ├─ Decode base64 → ImageScript
│       ├─ Apply blackout (top 40%)
│       └─ Encode JPEG → base64
│       ↓
│   Save to FileSystem (anonymized_*.jpg)
│       ↓
│   ✅ SUCCESS → return URIs
│
└─→ [Fallback] localPrivacyFilter()
        ├─ Try crop to bottom 60%
        └─ Fallback: extreme pixelation
        ↓
    ✅ SUCCESS → return URIs

Anonymized Image
    ↓
readImageAsBase64() [ai-client.ts]
    ↓
Create data URL: "data:image/jpeg;base64,..."
    ↓
Send to Edge Function (ai-proxy)
    ↓
OpenAI Vision API
    ↓
❌ Returns: "[Image #1]" (BUG)
```

### Edge Functions

**anonymize-faces** (v6):
- URL: `https://guyxczavhtemwlrknqpm.supabase.co/functions/v1/anonymize-faces`
- Input: `{ images: string[], method?: string, preserveRatio?: number }`
- Output: `{ images: string[], method: string, facesDetected?: number }`
- Library: ImageScript v1.2.15
- Methods: blackout (default), pixelate, blur

**ai-proxy** (v77):
- URL: `https://guyxczavhtemwlrknqpm.functions.supabase.co/ai-proxy`
- Handles: OpenAI, Perplexity, rate limiting, streaming
- Image format: `{ type: 'image_url', image_url: { url: 'data:image/jpeg;base64,...' } }`

---

## Próximos Pasos

### INMEDIATO: Resolver "[Image #1]" Bug

**Acción recomendada**: Implementar **Opción 1 (Crop)**

**Razones**:
1. El fallback local usa crop y sabemos que funciona con OpenAI
2. Elimina la barra negra que puede estar confundiendo a OpenAI
3. Reduce tamaño de imagen (menor costo)
4. Mismo nivel de privacidad

**Pasos**:
1. Modificar Edge Function para agregar método 'crop'
2. Cambiar default de 'blackout' a 'crop' en lib/faceAnonymizer.ts
3. Desplegar Edge Function v7
4. Probar con imagen real
5. Verificar que OpenAI devuelve análisis completo (no "[Image #1]")

### DESPUÉS: Merge a Main

Una vez resuelto el bug "[Image #1]":
1. Crear Pull Request desde `feature/face-anonymization`
2. Code review (verificar que todo funciona)
3. Merge a `main`
4. Monitorear producción

---

## Decisiones Técnicas Importantes

### ¿Por qué Edge Function separada vs integrar en ai-proxy?

**Decisión**: Crear `anonymize-faces` como Edge Function separada

**Razones**:
1. **Separation of Concerns**: ai-proxy maneja AI, anonymize-faces maneja privacidad
2. **Independent Scaling**: Anonymización es CPU-intensive, AI es I/O-intensive
3. **Error Isolation**: Si anonymization falla, AI sigue funcionando
4. **Deployment Independence**: Actualizar uno sin tocar el otro
5. **Cost Transparency**: Ver costos separados para cada servicio

### ¿Por qué ImageScript vs otras librerías?

**Decisión**: Usar ImageScript v1.2.15

**Razones**:
1. Pure TypeScript (funciona en Deno sin compilación)
2. No requiere dependencias nativas (sharp, etc.)
3. Disponible en Deno por defecto
4. Suficientemente rápido para nuestro caso de uso

**Trade-off**: ImageScript usa coordenadas 1-indexed (inusual), causó bugs iniciales

### ¿Por qué blackout como método default?

**Decisión inicial**: Blackout (barra negra sólida)

**Razones**:
- 100% efectivo (OpenAI no puede detectar caras)
- Más rápido que blur/pixelate
- Menor uso de CPU

**Problema descubierto**: OpenAI Vision API devuelve "[Image #1]" con blackout

**Decisión pendiente**: Cambiar a crop (ver próximos pasos)

---

## Logs de Referencia

### Successful Edge Function Call
```
LOG  Starting face anonymization for 1 images using method: blackout
LOG  Calling Edge Function with 1 images...
LOG  Edge Function processed 1 images
LOG  Server-side anonymization successful (server)
LOG  Images processed via server method
LOG  Detected and blurred 0 faces
```

### Successful Image Processing
```
LOG  [AIClient] Using 1 pre-processed local images
LOG  [AIClient] Reading image 1/1: ...anonymized_1762245673039_0.jpg
LOG  [AIClient] Successfully processed image 1, base64 length: 259280
LOG  [AIClient] Successfully processed 1 images as data URLs
```

### Problematic OpenAI Response
```
LOG  [AIClient] Attempt 1/3 - Use case: vision_analysis
LOG  [AIClient] Success! Cost: $0.0078, Latency: 2452ms
// Chat displays: "[Image #1]" instead of hair analysis
```

---

## Documentación Relacionada

- **Supabase Project**: `sessions/2025-10-21-supabase-credentials-setup.md`
- **Claude Code Agents**: `sessions/2025-10-23-claude-code-agents-setup.md`
- **Previous Attempts**: (ninguna documentación previa, este es el primer intento de anonymización)

---

---

## Update: 2025-11-04 12:48 UTC - Crop Method Implementation (v7)

### Solución Implementada

Implementado método 'crop' en Edge Function v7 para resolver el bug "[Image #1]" de OpenAI.

**Cambios realizados**:

1. **Edge Function** (`supabase/functions/anonymize-faces/index.ts`):
   - ✅ Agregada función `applyCrop()` (líneas 39-63)
   - ✅ Actualizada interfaz `AnonymizeRequest` para incluir 'crop'
   - ✅ Actualizada función `anonymizeImage()` para manejar caso 'crop'
   - ✅ Cambiado default de 'blackout' a 'crop'
   - ✅ Actualizada documentación header (v7 notes)

2. **Client** (`lib/faceAnonymizer.ts`):
   - ✅ Actualizada firma de `anonymizeFaces()` para incluir 'crop'
   - ✅ Cambiado método default de 'blackout' a 'crop' (línea 27)
   - ✅ Actualizada firma de `serverSideAnonymization()`

3. **Deployment**:
   - ✅ Edge Function v7 desplegada exitosamente
   - ✅ Status: ACTIVE
   - ✅ Version ID: 6d60baca-8ea2-4245-9d1b-8fa0386fbbd6

### Implementación Técnica del Crop

```typescript
async function applyCrop(imageBuffer: Uint8Array, preserveRatio: number): Promise<Uint8Array> {
  const image = await Image.decode(imageBuffer);
  const width = image.width;
  const height = image.height;

  // Calculate crop region (bottom 60% by default)
  const cropStartY = Math.floor(height * (1 - preserveRatio)) + 1; // +1 for 1-indexed
  const cropHeight = Math.floor(height * preserveRatio);

  // Create new image with only bottom portion
  const croppedImage = new Image(width, cropHeight);

  // Copy pixels from bottom region to new image
  for (let y = 1; y <= cropHeight; y++) {
    for (let x = 1; x <= width; x++) {
      const sourceY = cropStartY + y - 1;
      croppedImage.setPixelAt(x, y, image.getPixelAt(x, sourceY));
    }
  }

  return await croppedImage.encodeJPEG(90);
}
```

**Características**:
- Elimina completamente el 40% superior (donde están las caras)
- Devuelve solo el 60% inferior (donde está el cabello)
- Reduce tamaño de imagen (menor costo de OpenAI)
- Usa coordenadas 1-indexed correctamente
- Mismo nivel de privacidad que blackout

### Esperado vs Anterior

**ANTES (v6 - blackout)**:
```
✅ Edge Function procesa imagen (blackout top 40%)
✅ OpenAI recibe imagen con barra negra
❌ OpenAI devuelve "[Image #1]" (confundido por barra negra)
```

**AHORA (v7 - crop)**:
```
✅ Edge Function procesa imagen (crop to bottom 60%)
✅ OpenAI recibe imagen "normal" sin caras
✅ OpenAI debería devolver análisis completo de cabello
```

### Próximos Pasos

- [ ] **TESTING CRÍTICO**: Probar con imagen real en la app
- [ ] Verificar logs: debe decir "Crop: WxH, cropping from y=X, height=Ypx"
- [ ] Verificar que OpenAI devuelve análisis completo (NO "[Image #1]")
- [ ] Si funciona: Crear Pull Request
- [ ] Code review
- [ ] Merge a main
- [ ] Monitorear producción

### Testing Checklist

**Para validar que la implementación funciona**:

1. **Subir una imagen en la app** (chat o step1/step2)
2. **Revisar logs de Edge Function**:
   ```
   ✅ Processing 1 images with method: crop, preserve: 60%
   ✅ Crop: 800x1195, cropping from y=479, height=717px
   ```
3. **Revisar logs de AIClient**:
   ```
   ✅ [AIClient] Successfully processed image 1, base64 length: <menor que antes>
   ✅ [AIClient] Success! Cost: $<menor que antes>, Latency: <ms>
   ```
4. **Verificar respuesta de OpenAI**:
   - ❌ NO debe contener "[Image #1]"
   - ✅ DEBE contener análisis de cabello (color, textura, longitud, etc.)

### Rollback Plan

Si el crop no funciona (OpenAI sigue devolviendo "[Image #1]"):

**Opción A**: Cambiar a pixelate
```typescript
// En lib/faceAnonymizer.ts línea 27
method: 'crop' | 'blackout' | 'pixelate' | 'blur' = 'pixelate'
```

**Opción B**: Reducir área de crop (preservar 70% en lugar de 60%)
```typescript
// En lib/faceAnonymizer.ts línea 98
preserveRatio: 0.7 // Preserve bottom 70%
```

---

## TODOs

- [x] Implementar método 'crop' en Edge Function
- [x] Cambiar default a 'crop' en client
- [x] Desplegar y probar Edge Function v7
- [ ] **Verificar que OpenAI devuelve análisis completo** (TESTING PENDIENTE)
- [ ] Crear Pull Request
- [ ] Code review
- [ ] Merge a main
- [ ] Monitorear producción

---

**Última actualización**: 2025-11-04 12:48 UTC
