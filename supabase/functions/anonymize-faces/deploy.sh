#!/bin/bash

# Face Anonymization Edge Function - Deployment Script
# Usage: ./deploy.sh [--local | --production | --test]

set -e  # Exit on error

PROJECT_ID="guyxczavhtemwlrknqpm"
FUNCTION_NAME="anonymize-faces"

echo "=================================================="
echo "Face Anonymization Edge Function Deployment"
echo "=================================================="
echo ""

# Parse arguments
MODE="${1:---production}"

case "$MODE" in
  --local)
    echo "🔧 Starting LOCAL development server..."
    echo ""
    echo "Commands:"
    echo "  1. bun x supabase start (in another terminal)"
    echo "  2. bun x supabase functions serve $FUNCTION_NAME"
    echo ""
    echo "Test with:"
    echo "  curl -X POST http://localhost:54321/functions/v1/$FUNCTION_NAME \\"
    echo "    -H 'Content-Type: application/json' \\"
    echo "    -d '{\"images\":[\"BASE64_HERE\"],\"method\":\"blackout\"}'"
    echo ""
    read -p "Press Enter to start local server..."
    bun x supabase functions serve "$FUNCTION_NAME"
    ;;

  --test)
    echo "🧪 Running TESTS..."
    echo ""
    if ! command -v deno &> /dev/null; then
      echo "❌ Error: Deno not found. Install: https://deno.land/"
      exit 1
    fi
    
    echo "Testing Edge Function locally..."
    cd "$(dirname "$0")"
    deno run --allow-net --allow-read test.ts
    ;;

  --production)
    echo "🚀 Deploying to PRODUCTION..."
    echo ""
    echo "Project: $PROJECT_ID"
    echo "Function: $FUNCTION_NAME"
    echo ""
    
    # Check if supabase CLI is available
    if ! command -v supabase &> /dev/null; then
      echo "❌ Error: Supabase CLI not found"
      echo "Install: bun i -g supabase"
      exit 1
    fi
    
    # Link project if not already linked
    echo "📦 Linking to Supabase project..."
    bun x supabase link --project-ref "$PROJECT_ID" 2>/dev/null || true
    
    # Deploy function
    echo ""
    echo "🚢 Deploying function..."
    bun x supabase functions deploy "$FUNCTION_NAME"
    
    # Verify deployment
    echo ""
    echo "✅ Deployment complete!"
    echo ""
    echo "🔍 Verifying deployment..."
    bun x supabase functions list | grep "$FUNCTION_NAME" || echo "⚠️  Function not found in list"
    
    echo ""
    echo "=================================================="
    echo "✅ DEPLOYMENT SUCCESS"
    echo "=================================================="
    echo ""
    echo "Test with:"
    echo "  curl -X POST https://$PROJECT_ID.supabase.co/functions/v1/$FUNCTION_NAME \\"
    echo "    -H 'Authorization: Bearer \$EXPO_PUBLIC_SUPABASE_ANON_KEY' \\"
    echo "    -H 'Content-Type: application/json' \\"
    echo "    -d '{\"images\":[\"BASE64\"],\"method\":\"blackout\"}'"
    echo ""
    echo "Monitor logs:"
    echo "  bun x supabase functions logs $FUNCTION_NAME"
    echo ""
    ;;

  *)
    echo "❌ Invalid mode: $MODE"
    echo ""
    echo "Usage: $0 [--local | --production | --test]"
    echo ""
    echo "Modes:"
    echo "  --local       Start local development server"
    echo "  --test        Run test suite"
    echo "  --production  Deploy to production (default)"
    exit 1
    ;;
esac
