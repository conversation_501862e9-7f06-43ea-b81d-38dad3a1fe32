/**
 * Face Anonymization Edge Function (v8)
 *
 * Detect faces via Google Vision and pixelate only the detected regions.
 * This keeps hair and other context intact while removing biometric detail.
 */

import "jsr:@supabase/functions-js/edge-runtime.d.ts";

import { Image } from "https://deno.land/x/imagescript@1.2.15/mod.ts";

interface AnonymizeRequest {
  images: string[];
  // Legacy fields kept for backward compatibility (ignored in processing)
  method?: string;
  preserveRatio?: number;
}

interface AnonymizeResponse {
  images: string[];
  facesDetected: number;
  method: string;
  facesByImage?: number[];
  error?: string;
}

interface GoogleCredentials {
  client_email: string;
  private_key: string;
  token_uri?: string;
}

interface BoundingVertex {
  x?: number;
  y?: number;
}

interface FaceAnnotation {
  boundingPoly?: { vertices?: BoundingVertex[] };
  fdBoundingPoly?: { vertices?: BoundingVertex[] };
}

const VISION_SCOPE = "https://www.googleapis.com/auth/cloud-vision";
const DEFAULT_TOKEN_URI = "https://oauth2.googleapis.com/token";
const VISION_ENDPOINT = "https://vision.googleapis.com/v1/images:annotate";

const encoder = new TextEncoder();

let cachedToken: { token: string; expiresAt: number } | null = null;
let cachedCredentials: GoogleCredentials | null = null;

function readCredentials(): GoogleCredentials {
  if (cachedCredentials) {
    return cachedCredentials;
  }

  const credentialsRaw = Deno.env.get("GOOGLE_VISION_CREDENTIALS");
  if (!credentialsRaw) {
    throw new Error("Missing GOOGLE_VISION_CREDENTIALS secret");
  }

  const parsed = JSON.parse(credentialsRaw) as GoogleCredentials;
  if (!parsed.client_email || !parsed.private_key) {
    throw new Error("Invalid Google Vision credentials: client_email/private_key required");
  }

  cachedCredentials = parsed;
  return parsed;
}

function base64UrlEncodeString(input: string): string {
  const bytes = encoder.encode(input);
  let binary = "";
  for (let i = 0; i < bytes.length; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary).replace(/\+/g, "-").replace(/\//g, "_").replace(/=+$/g, "");
}

function base64UrlEncodeBytes(bytes: Uint8Array): string {
  let binary = "";
  const chunkSize = 0x8000;
  for (let i = 0; i < bytes.length; i += chunkSize) {
    binary += String.fromCharCode(...bytes.subarray(i, i + chunkSize));
  }
  return btoa(binary).replace(/\+/g, "-").replace(/\//g, "_").replace(/=+$/g, "");
}

function pemToArrayBuffer(pem: string): ArrayBuffer {
  const cleaned = pem
    .replace("-----BEGIN PRIVATE KEY-----", "")
    .replace("-----END PRIVATE KEY-----", "")
    .replace(/\s+/g, "");
  const binary = atob(cleaned);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return bytes.buffer;
}

async function getGoogleAccessToken(): Promise<string> {
  const now = Math.floor(Date.now() / 1000);
  if (cachedToken && cachedToken.expiresAt - 60 > now) {
    return cachedToken.token;
  }

  const credentials = readCredentials();
  const tokenUri = credentials.token_uri ?? DEFAULT_TOKEN_URI;
  const header = { alg: "RS256", typ: "JWT" };
  const payload = {
    iss: credentials.client_email,
    scope: VISION_SCOPE,
    aud: tokenUri,
    exp: now + 3600,
    iat: now,
  };

  const encodedHeader = base64UrlEncodeString(JSON.stringify(header));
  const encodedPayload = base64UrlEncodeString(JSON.stringify(payload));
  const jwtToSign = `${encodedHeader}.${encodedPayload}`;

  const keyData = pemToArrayBuffer(credentials.private_key);
  const key = await crypto.subtle.importKey(
    "pkcs8",
    keyData,
    { name: "RSASSA-PKCS1-v1_5", hash: "SHA-256" },
    false,
    ["sign"],
  );

  const signatureBuffer = await crypto.subtle.sign(
    "RSASSA-PKCS1-v1_5",
    key,
    encoder.encode(jwtToSign),
  );
  const signature = base64UrlEncodeBytes(new Uint8Array(signatureBuffer));
  const assertion = `${jwtToSign}.${signature}`;

  const body = new URLSearchParams({
    grant_type: "urn:ietf:params:oauth:grant-type:jwt-bearer",
    assertion,
  });

  const response = await fetch(tokenUri, {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body,
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to fetch Google access token: ${response.status} ${errorText}`);
  }

  const tokenPayload = await response.json();
  const accessToken = tokenPayload.access_token as string | undefined;
  const expiresIn = tokenPayload.expires_in as number | undefined;

  if (!accessToken || !expiresIn) {
    throw new Error("Invalid token response from Google");
  }

  cachedToken = {
    token: accessToken,
    expiresAt: now + expiresIn,
  };

  return accessToken;
}

async function detectFaces(base64Image: string): Promise<FaceAnnotation[]> {
  const accessToken = await getGoogleAccessToken();
  const response = await fetch(VISION_ENDPOINT, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      requests: [
        {
          image: { content: base64Image },
          features: [{ type: "FACE_DETECTION", maxResults: 10 }],
        },
      ],
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Google Vision API error: ${response.status} ${errorText}`);
  }

  const json = await response.json();
  const firstResponse = json.responses?.[0];

  if (firstResponse?.error) {
    throw new Error(`Google Vision API responded with error: ${firstResponse.error.message ?? "Unknown"}`);
  }

  const annotations = firstResponse?.faceAnnotations;
  if (!Array.isArray(annotations)) {
    return [];
  }

  return annotations as FaceAnnotation[];
}

function bufferToBase64(buffer: Uint8Array): string {
  const chunkSize = 32768;
  let binary = "";
  for (let i = 0; i < buffer.length; i += chunkSize) {
    const chunk = buffer.slice(i, i + chunkSize);
    binary += String.fromCharCode(...chunk);
  }
  return btoa(binary);
}

function base64ToBuffer(base64: string): Uint8Array {
  const binary = atob(base64);
  const buffer = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    buffer[i] = binary.charCodeAt(i);
  }
  return buffer;
}

function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

function pixelateFaceRegion(image: Image, vertices: BoundingVertex[]): void {
  if (!vertices || vertices.length === 0) {
    return;
  }

  const xs = vertices.map((vertex) => vertex.x ?? 0);
  const ys = vertices.map((vertex) => vertex.y ?? 0);

  let minX = Math.min(...xs);
  let maxX = Math.max(...xs);
  let minY = Math.min(...ys);
  let maxY = Math.max(...ys);

  // Slight padding so the entire face remains covered.
  const paddingX = Math.max(4, Math.round((maxX - minX) * 0.1));
  const paddingY = Math.max(4, Math.round((maxY - minY) * 0.1));

  minX -= paddingX;
  maxX += paddingX;
  minY -= paddingY;
  maxY += paddingY;

  const imageWidth = image.width;
  const imageHeight = image.height;

  const startX = clamp(Math.floor(minX), 0, imageWidth - 1);
  const endX = clamp(Math.ceil(maxX), 0, imageWidth - 1);
  const startY = clamp(Math.floor(minY), 0, imageHeight - 1);
  const endY = clamp(Math.ceil(maxY), 0, imageHeight - 1);

  const regionWidth = endX - startX + 1;
  const regionHeight = endY - startY + 1;
  if (regionWidth <= 0 || regionHeight <= 0) {
    return;
  }

  const pixelSizeBase = Math.floor(Math.min(regionWidth, regionHeight) / 6);
  const pixelSize = Math.max(8, pixelSizeBase);

  for (let y = startY; y <= endY; y += pixelSize) {
    for (let x = startX; x <= endX; x += pixelSize) {
      const sampleX = clamp(x, startX, endX) + 1; // ImageScript is 1-indexed
      const sampleY = clamp(y, startY, endY) + 1;
      const color = image.getPixelAt(sampleX, sampleY);

      for (let dy = 0; dy < pixelSize && y + dy <= endY; dy++) {
        for (let dx = 0; dx < pixelSize && x + dx <= endX; dx++) {
          image.setPixelAt(x + dx + 1, y + dy + 1, color);
        }
      }
    }
  }
}

async function anonymizeImage(base64Image: string): Promise<{ image: string; faces: number }> {
  const faceAnnotations = await detectFaces(base64Image);

  if (faceAnnotations.length === 0) {
    return { image: base64Image, faces: 0 };
  }

  const imageData = base64ToBuffer(base64Image);
  const image = await Image.decode(imageData);

  for (const annotation of faceAnnotations) {
    const vertices = annotation.fdBoundingPoly?.vertices ?? annotation.boundingPoly?.vertices;
    if (!vertices || vertices.length === 0) {
      continue;
    }
    pixelateFaceRegion(image, vertices);
  }

  const processedBuffer = await image.encodeJPEG(90);
  return { image: bufferToBase64(processedBuffer), faces: faceAnnotations.length };
}

Deno.serve(async (req: Request) => {
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  try {
    const { images }: AnonymizeRequest = await req.json();

    if (!images || !Array.isArray(images) || images.length === 0) {
      return new Response(
        JSON.stringify({ error: "Invalid request: images array required" }),
        { status: 400, headers: { "Content-Type": "application/json" } },
      );
    }

    console.log(`Processing ${images.length} images with Google Vision anonymization`);

    const results = await Promise.all(images.map((img) => anonymizeImage(img)));
    const processedImages = results.map((result) => result.image);
    const facesByImage = results.map((result) => result.faces);
    const facesDetected = facesByImage.reduce((acc, count) => acc + count, 0);

    const response: AnonymizeResponse = {
      images: processedImages,
      method: "pixelate",
      facesDetected,
      facesByImage,
    };

    return new Response(JSON.stringify(response), {
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
    });
  } catch (error) {
    console.error("Edge function error:", error);

    return new Response(
      JSON.stringify({
        error: "Failed to anonymize images",
        details: error instanceof Error ? error.message : String(error),
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      },
    );
  }
});
