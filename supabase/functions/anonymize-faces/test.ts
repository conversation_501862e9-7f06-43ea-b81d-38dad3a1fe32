/**
 * Local test script for anonymize-faces Edge Function
 * Run with: deno run --allow-net --allow-read test.ts
 */

import { readFileSync } from "https://deno.land/std@0.177.0/node/fs.ts";

const EDGE_FUNCTION_URL = 'http://localhost:54321/functions/v1/anonymize-faces';
// For production: 'https://guyxczavhtemwlrknqpm.supabase.co/functions/v1/anonymize-faces'

async function testAnonymization() {
  console.log('Testing Face Anonymization Edge Function...\n');

  // Create a simple test image (1x1 black pixel as base64)
  const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==';

  const methods = ['blackout', 'pixelate', 'blur'];

  for (const method of methods) {
    console.log(`\nTesting method: ${method}`);
    console.log('='.repeat(50));

    const startTime = Date.now();

    try {
      const response = await fetch(EDGE_FUNCTION_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // For production, add: 'Authorization': 'Bearer YOUR_ANON_KEY'
        },
        body: JSON.stringify({
          images: [testImageBase64],
          method: method,
          preserveRatio: 0.6
        })
      });

      const elapsed = Date.now() - startTime;

      if (!response.ok) {
        const error = await response.text();
        console.error(`❌ Failed (${response.status}): ${error}`);
        continue;
      }

      const data = await response.json();
      console.log(`✅ Success in ${elapsed}ms`);
      console.log(`   - Images processed: ${data.images.length}`);
      console.log(`   - Method used: ${data.method}`);
      console.log(`   - Output size: ${data.images[0].length} bytes`);

    } catch (error) {
      console.error(`❌ Error: ${error.message}`);
    }
  }

  console.log('\n' + '='.repeat(50));
  console.log('Testing complete!');
}

// Run tests
testAnonymization();
