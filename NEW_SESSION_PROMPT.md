# PROMPT PARA NUEVA SESIÓN - Face Anonymization B<PERSON>, necesito continuar trabajando en un bug relacionado con face anonymization en la app Salonier AI.

## Contexto Completo

Lee primero este archivo para entender todo lo que hemos hecho:
```
sessions/2025-11-04-face-anonymization-implementation.md
```

## Resumen Ejecutivo

Hemos implementado un sistema de anonymización de caras para resolver VisionSafetyError de OpenAI Vision API. La Edge Function funciona perfectamente (v6 en producción), pero OpenAI está devolviendo `[Image #1]` en lugar del análisis de cabello real.

**Estado actual**:
- ✅ Edge Function `anonymize-faces` v6 desplegada y funcional
- ✅ Cliente integrado en chat.tsx, step1.tsx, step2.tsx, step5.tsx
- ✅ NO más RangeError, NO más stack overflow, NO más fallback local
- ❌ OpenAI devuelve "[Image #1]" en lugar de análisis de cabello

## El Bug Específico

**Síntoma**:
Cuando enviamos una imagen anonymizada a OpenAI Vision API (via Edge Function ai-proxy), OpenAI procesa la imagen (cobra $0.0078) pero devuelve el texto literal `[Image #1]` en lugar del análisis de cabello.

**Logs que confirman el bug**:
```
✅ Edge Function processed 1 images
✅ Server-side anonymization successful (server)
✅ base64 length: 259280
✅ [AIClient] Success! Cost: $0.0078, Latency: 2452ms
❌ Chat displays: "[Image #1]"
```

**Hipótesis principal**:
El método 'blackout' (barra negra sólida en top 40%) está interfiriendo con la capacidad de OpenAI de analizar la imagen, causando que devuelva un placeholder.

## Lo Que Necesito Que Hagas

**Implementar Opción 1: Método 'crop'**

Necesito que modifiques la Edge Function para agregar un método 'crop' que:
1. Elimine completamente el 40% superior (donde están las caras)
2. Devuelva solo el 60% inferior (donde está el cabello)
3. Esto es lo que hace el fallback local y sabemos que funciona con OpenAI

**Archivos a modificar**:

1. **Edge Function** (`supabase/functions/anonymize-faces/index.ts`):
   - Agregar función `applyCrop()` similar a `applyBlackout()`
   - Crear nueva imagen solo con bottom 60%
   - Recordar: ImageScript usa coordenadas 1-indexed (x=1, y=1 como inicio)

2. **Client** (`lib/faceAnonymizer.ts`):
   - Cambiar método default de 'blackout' a 'crop'
   - Línea 27: `method: 'blackout' | 'pixelate' | 'blur' | 'crop' = 'crop'`
   - Línea 98: `preserveRatio: 0.6` (mantener)

3. **TypeScript types** (si es necesario actualizar interfaces)

**Después de implementar**:
1. Desplegar Edge Function v7 a Supabase (project ID: `guyxczavhtemwlrknqpm`)
2. Probar con una imagen real en la app
3. Verificar que OpenAI devuelve análisis completo (no "[Image #1]")

## Información Técnica Importante

**ImageScript usa coordenadas 1-indexed**:
```typescript
// ✅ CORRECTO
for (let y = 1; y <= height; y++) {
  for (let x = 1; x <= width; x++) {
    image.setPixelAt(x, y, color);
  }
}

// ❌ INCORRECTO (causa RangeError)
for (let y = 0; y < height; y++) {
  for (let x = 0; x < width; x++) {
    image.setPixelAt(x, y, color);
  }
}
```

**Chunked encoding para imágenes grandes** (ya implementado en v6):
```typescript
function bufferToBase64(buffer: Uint8Array): string {
  const chunkSize = 32768;
  let binary = '';
  for (let i = 0; i < buffer.length; i += chunkSize) {
    const chunk = buffer.slice(i, i + chunkSize);
    binary += String.fromCharCode(...chunk);
  }
  return btoa(binary);
}
```

## Archivos Clave a Revisar

- `supabase/functions/anonymize-faces/index.ts` - Edge Function (líneas 39-58 para referencia de applyBlackout)
- `lib/faceAnonymizer.ts` - Cliente (línea 27 para método default)
- `types/index.ts` - Si necesitas actualizar tipos

## Cómo Desplegar

Usa el MCP de Supabase:
```typescript
await mcp__supabase__deploy_edge_function({
  project_id: 'guyxczavhtemwlrknqpm',
  name: 'anonymize-faces',
  files: [{ name: 'index.ts', content: '...' }],
  entrypoint_path: 'index.ts'
});
```

## Expectativa de Resultado

Después de implementar crop:
- ✅ Edge Function devuelve imagen cropped (solo bottom 60%)
- ✅ Imagen tiene menor tamaño (menor costo)
- ✅ OpenAI recibe imagen "normal" sin barra negra
- ✅ OpenAI devuelve análisis de cabello completo (NO "[Image #1]")

## Branch y Git

**Branch actual**: `feature/face-anonymization` (ya pusheada a GitHub)
**Último commit**: `bcb9055 - fix: Use chunked base64 encoding for large images`

Después de implementar crop, necesitarás:
1. Commit los cambios
2. Push a GitHub
3. Probar en producción
4. Si funciona, crear PR a main

## Preguntas Frecuentes

**P: ¿Por qué crop y no reducir el blackout a 30%?**
R: El fallback local usa crop y sabemos que funciona con OpenAI. Es la opción más segura.

**P: ¿Crop mantiene el mismo nivel de privacidad?**
R: Sí, las caras se eliminan completamente (no están en el 60% inferior).

**P: ¿Qué pasa con el preserveRatio?**
R: Mantener 0.6 (60%). Crop en cropStartY = 40%, cropHeight = 60%.

---

**¿Listo para implementar? Por favor, lee primero sessions/2025-11-04-face-anonymization-implementation.md para el contexto completo.**
