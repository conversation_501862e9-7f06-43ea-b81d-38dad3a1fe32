import React, { useMemo } from 'react';
import { StyleSheet, Text, View, type StyleProp, type ViewStyle } from 'react-native';
import Colors from '@/constants/colors';
import { textStyles } from '@/constants/typography';
import { space } from '@/constants/spacing';
import { useAuth } from '@/contexts/AuthContext';

type TimeSlot = 'morning' | 'afternoon' | 'evening' | 'late';

type PromptGenerator = (args: { greeting: string; name?: string }) => string;

export type ChatWelcomeHeroProps = {
  variant?: 'main' | 'formula';
  style?: StyleProp<ViewStyle>;
};

const getTimeSlot = (date: Date): TimeSlot => {
  const hour = date.getHours();

  if (hour >= 6 && hour < 12) {
    return 'morning';
  }
  if (hour >= 12 && hour < 18) {
    return 'afternoon';
  }
  if (hour >= 18 && hour < 22) {
    return 'evening';
  }
  return 'late';
};

const greetings: Record<TimeSlot, string> = {
  morning: 'Buenos días',
  afternoon: 'Buenas tardes',
  evening: 'Buenas noches',
  late: 'Buenas noches',
};

const mainPrompts: Record<TimeSlot, PromptGenerator[]> = {
  morning: [
    ({ greeting, name }) => `${greeting}${name ? ` ${name}` : ''}, ¿en qué te acompaño esta mañana?`,
    ({ name }) => `¿Qué reto de color resolvemos hoy${name ? `, ${name}` : ''}?`,
    ({ name }) => `¿Revisamos tu agenda de servicios de hoy${name ? `, ${name}` : ''}?`,
  ],
  afternoon: [
    ({ greeting, name }) => `${greeting}${name ? ` ${name}` : ''}, ¿qué idea buscabas ahora?`,
    ({ name }) => `¿Buscamos una idea nueva para tu siguiente servicio${name ? `, ${name}` : ''}?`,
    ({ name }) => `¿Afinamos la recomendación antes de tu próxima cita${name ? `, ${name}` : ''}?`,
  ],
  evening: [
    ({ greeting, name }) => `${greeting}${name ? ` ${name}` : ''}, ¿qué dejamos listo antes de cerrar?`,
    ({ name }) => `¿Qué pendiente resolvemos antes de terminar el día${name ? `, ${name}` : ''}?`,
    ({ name }) => `¿Documentamos aprendizajes clave de hoy${name ? `, ${name}` : ''}?`,
  ],
  late: [
    ({ greeting, name }) => `${greeting}${name ? ` ${name}` : ''}, ¿dejamos lista la estrategia de mañana?`,
    ({ name }) => `¿Qué detalle afinamos mientras está fresco${name ? `, ${name}` : ''}?`,
    ({ name }) => `¿Te preparo ideas para primera hora${name ? `, ${name}` : ''}?`,
  ],
};

const formulaPrompts: Record<TimeSlot, PromptGenerator[]> = {
  morning: [
    ({ greeting, name }) => `${greeting}${name ? ` ${name}` : ''}, ¿validamos la fórmula del primer servicio?`,
    ({ name }) => `¿Ajustamos mezclas y oxidantes antes de empezar${name ? `, ${name}` : ''}?`,
    ({ name }) => `¿Confirmamos tiempos para lograr el objetivo de hoy${name ? `, ${name}` : ''}?`,
  ],
  afternoon: [
    ({ greeting, name }) => `${greeting}${name ? ` ${name}` : ''}, ¿perfeccionamos la fórmula de la siguiente sesión?`,
    ({ name }) => `¿Comparo alternativas de marca contigo${name ? `, ${name}` : ''}?`,
    ({ name }) => `¿Revisamos proporciones antes de aplicar${name ? `, ${name}` : ''}?`,
  ],
  evening: [
    ({ greeting, name }) => `${greeting}${name ? ` ${name}` : ''}, ¿documentamos la fórmula final antes de cerrar?`,
    ({ name }) => `¿Dejamos listo el plan técnico de mañana${name ? `, ${name}` : ''}?`,
    ({ name }) => `¿Confirmamos riesgos y cuidados post-servicio${name ? `, ${name}` : ''}?`,
  ],
  late: [
    ({ greeting, name }) => `${greeting}${name ? ` ${name}` : ''}, ¿registramos mezclas y notas para mañana?`,
    ({ name }) => `¿Ajustamos la fórmula mientras lo recuerdas${name ? `, ${name}` : ''}?`,
    ({ name }) => `¿Revisamos el plan técnico antes de desconectar${name ? `, ${name}` : ''}?`,
  ],
};

const getPrompt = (
  pool: PromptGenerator[],
  fallback: PromptGenerator[],
  args: { greeting: string; name?: string }
) => {
  const source = pool.length > 0 ? pool : fallback;
  const index = Math.floor(Math.random() * source.length);
  return source[index](args);
};

export default function ChatWelcomeHero({ variant = 'main', style }: ChatWelcomeHeroProps) {
  const { profile } = useAuth();

  const now = useMemo(() => new Date(), []);
  const slot = useMemo(() => getTimeSlot(now), [now]);
  const greeting = greetings[slot];

  const firstName = useMemo(() => {
    if (!profile?.name) return undefined;
    return profile.name.split(' ')[0];
  }, [profile?.name]);

  const prompt = useMemo(() => {
    const pool = variant === 'formula' ? formulaPrompts[slot] : mainPrompts[slot];
    const fallback = variant === 'formula' ? formulaPrompts.morning : mainPrompts.morning;
    return getPrompt(pool, fallback, { greeting, name: firstName });
  }, [variant, slot, greeting, firstName]);

  return (
    <View style={[styles.container, style]}>
      <View style={styles.logoWrapper}>
        <Text style={styles.logoText}>S</Text>
      </View>
      <Text style={styles.prompt}>{prompt}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    paddingVertical: space.xxl,  // 24px
    paddingHorizontal: space.lg,  // 16px
    alignItems: 'center',
    gap: space.lg,  // 16px
  },
  logoWrapper: {
    width: 76,
    height: 76,
    borderRadius: 38,
    backgroundColor: `${Colors.light.primary}15`,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: `${Colors.light.primary}35`,
  },
  logoText: {
    fontSize: 34,
    fontWeight: '800' as const,
    color: Colors.light.primary,
    letterSpacing: -0.8,
  },
  prompt: {
    // Claude mobile real: ~32-36px welcome text (from screenshots)
    ...textStyles.displayMedium,  // 32px, 700 weight, -0.6 spacing, 40 lineHeight
    color: Colors.light.text,
    textAlign: 'center',
  },
});
