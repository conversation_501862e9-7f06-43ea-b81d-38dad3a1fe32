import React, { useMemo } from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
  type TextStyle,
  type ViewStyle,
} from 'react-native';
import { Plus, Send } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { textStyles } from '@/constants/typography';
import { componentSpacing, borderRadius, shadow, touchTarget, space } from '@/constants/spacing';

type ChatComposerVariant = 'chat' | 'formula';

interface ChatComposerProps {
  value: string;
  placeholder: string;
  onChangeText: (text: string) => void;
  onSend: () => void;
  disabled?: boolean;
  isSending?: boolean;
  showSendingIndicator?: boolean;
  maxLength?: number;
  onPressAttachment?: () => void;
  onLongPressAttachment?: () => void;
  onFocusInput?: () => void;
  sendButtonTestID?: string;
  variant?: ChatComposerVariant;
}

type VariantStyleOverrides = {
  container?: ViewStyle;
  attachmentButton?: ViewStyle;
  attachmentButtonDisabled?: ViewStyle;
  input?: TextStyle;
  sendButton?: ViewStyle;
  sendButtonDisabled?: ViewStyle;
};

const variantStyles: Record<ChatComposerVariant, VariantStyleOverrides> = {
  chat: {},
  formula: {
    container: {
      gap: space.md,  // 12px
      borderRadius: borderRadius.massive,  // 32px
    },
    attachmentButton: {
      backgroundColor: Colors.light.surface,
    },
    input: {
      backgroundColor: Colors.light.background,
      borderRadius: borderRadius.lg,  // 16px
      paddingHorizontal: space.xs,  // 4px
      fontSize: 16,
    },
    sendButton: {
      width: touchTarget.comfortable,  // 48px
      height: touchTarget.comfortable,  // 48px
      borderRadius: touchTarget.comfortable / 2,  // 24px
    },
    sendButtonDisabled: {
      opacity: 0.42,
    },
  },
};

const DEFAULT_MAX_LENGTH = 500;

/**
 * ChatComposer - Claude-inspired input component
 *
 * Based on real Claude mobile screenshots (not web):
 * - Height: ~70-90px (compact but generous)
 * - Border radius: 36px (signature rounded)
 * - Padding: Comfortable 22/18px
 * - Can grow with multiline up to maxHeight 160px
 *
 * Key insight: Initial research mentioned 140px, but real mobile screenshots
 * show Claude uses ~70-90px for better screen real estate efficiency.
 */

const ChatComposer: React.FC<ChatComposerProps> = ({
  value,
  placeholder,
  onChangeText,
  onSend,
  disabled = false,
  isSending = false,
  showSendingIndicator = false,
  maxLength = DEFAULT_MAX_LENGTH,
  onPressAttachment,
  onLongPressAttachment,
  onFocusInput,
  sendButtonTestID,
  variant = 'chat',
}) => {
  const overrides = useMemo(() => variantStyles[variant], [variant]);

  const isAttachmentDisabled = !onPressAttachment;
  const isSendDisabled = disabled || isSending;
  const shouldShowSpinner = showSendingIndicator && isSending;

  const handleSend = () => {
    if (!isSendDisabled) {
      onSend();
    }
  };

  return (
    <View style={[styles.container, overrides.container]}>
      <TouchableOpacity
        accessibilityRole="button"
        style={[
          styles.attachmentButton,
          overrides.attachmentButton,
          isAttachmentDisabled && styles.attachmentButtonDisabled,
          isAttachmentDisabled && overrides.attachmentButtonDisabled,
        ]}
        onPress={onPressAttachment}
        onLongPress={onLongPressAttachment}
        disabled={isAttachmentDisabled}
      >
        <Plus color={Colors.light.primary} size={24} />
      </TouchableOpacity>

      <TextInput
        style={[styles.input, overrides.input]}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={Colors.light.textTertiary}
        multiline
        maxLength={maxLength}
        textAlignVertical="top"
        onFocus={onFocusInput}
        accessibilityLabel="Chat input"
      />

      <TouchableOpacity
        accessibilityRole="button"
        testID={sendButtonTestID}
        style={[
          styles.sendButton,
          overrides.sendButton,
          isSendDisabled && styles.sendButtonDisabled,
          isSendDisabled && overrides.sendButtonDisabled,
        ]}
        onPress={handleSend}
        disabled={isSendDisabled}
      >
        {shouldShowSpinner ? (
          <ActivityIndicator size="small" color={Colors.light.background} />
        ) : (
          <Send color={Colors.light.background} size={22} />
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: componentSpacing.composer.gap,  // 12px
    backgroundColor: Colors.light.background,
    borderRadius: borderRadius.input,  // 36px (Claude signature)
    borderWidth: componentSpacing.composer.borderWidth,  // 1.2px
    borderColor: Colors.light.border,
    paddingHorizontal: componentSpacing.composer.paddingHorizontal,  // 22px
    paddingVertical: componentSpacing.composer.paddingVertical,  // 18px
    minHeight: componentSpacing.composer.minHeight,  // 90px (Claude mobile real)
    ...shadow.md,  // Consistent shadow preset
  },
  attachmentButton: {
    width: touchTarget.comfortable,  // 48px
    height: touchTarget.comfortable,  // 48px
    borderRadius: touchTarget.comfortable / 2,  // 24px
    backgroundColor: Colors.light.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
    ...shadow.sm,  // Subtle shadow
  },
  attachmentButtonDisabled: {
    opacity: 0.4,
  },
  input: {
    flex: 1,
    ...textStyles.input,  // 17px, 28 lineHeight, -0.2 spacing
    color: Colors.light.text,
    maxHeight: 160,  // Can grow with multiline
    paddingVertical: 0,
    paddingHorizontal: space.xs,  // 4px
  },
  sendButton: {
    width: touchTarget.large,  // 52px
    height: touchTarget.large,  // 52px
    borderRadius: touchTarget.large / 2,  // 26px
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    ...shadow.primary,  // Primary accent shadow
  },
  sendButtonDisabled: {
    opacity: 0.4,
  },
});

export default ChatComposer;
